package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.CacheService;
import vn.vnpay.tvan.apps.reconciliation.service.DailyReportService;
import vn.vnpay.tvan.libs.common.constant.ReportStatus;
import vn.vnpay.tvan.libs.common.model.entity.DailyReportStatus;

import java.time.Instant;
import java.time.LocalDate;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Slf4j
@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.daily-reconciliation-report", havingValue = "true")
public class DailyReportJob {

    private final DailyReportService dailyReportService;

    private final CacheService cacheService;

    public DailyReportJob(DailyReportService dailyReportService,
                          CacheService cacheService) {
        this.dailyReportService = dailyReportService;
        this.cacheService = cacheService;
    }

    @Scheduled(cron = "${scheduler.job.cron.daily-reconciliation-report}")
    public void process() {
        log.info("Daily Message Report Job start");
        MDC.put(TRACE_ID, String.valueOf(System.nanoTime()));
        dailyReportService.saveDailyReportStatus(DailyReportStatus.builder()
                .reportDate(LocalDate.now().minusDays(1))
                .status(ReportStatus.UNSENT.value())
                .reportTime(Instant.now())
                .build());

        if (cacheService.isAuthorized()) {
            log.info("Daily Message Report Job when authorization session is ON");
        } else {
            try {
                dailyReportService.sendDailyMessageReportToTCT();
                log.info("Process Daily Message Report Job successfully");
            } catch (Exception ex) {
                log.error("Failed to process Daily Message Report Job", ex);
            } finally {
                MDC.clear();
            }
        }
    }
}
