package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import vn.vnpay.tvan.apps.reconciliation.model.projection.AboutToExpireProjection;
import vn.vnpay.tvan.apps.reconciliation.repository.EmailHistoryRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.ExpiredCustomerRepository;
import vn.vnpay.tvan.apps.reconciliation.service.EmailService;
import vn.vnpay.tvan.apps.reconciliation.service.ExpiredCustomerService;
import vn.vnpay.tvan.apps.reconciliation.service.mapper.AboutToExpireCustomerMapper;
import vn.vnpay.tvan.libs.common.model.entity.AboutToExpireCustomer;
import vn.vnpay.tvan.libs.common.model.entity.EmailHistory;

import javax.mail.MessagingException;
import java.time.Instant;
import java.util.List;

import static java.time.Instant.now;
import static vn.vnpay.tvan.apps.reconciliation.constant.Constant.EMAIL_SENDER;
import static vn.vnpay.tvan.apps.reconciliation.constant.TemplateEmail.ABOUT_TO_EXPIRE_CUSTOMER;
import static vn.vnpay.tvan.libs.common.constant.EmailHistoryType.CUSTOMER;
import static vn.vnpay.tvan.libs.common.enumeration.AboutToExpireCustomerStatus.ABOUT_TO_EXPIRE;
import static vn.vnpay.tvan.libs.common.util.Utils.nonNullOrEmpty;

@Service
@Slf4j
public class ExpiredCustomerServiceImpl implements ExpiredCustomerService {
    private final ExpiredCustomerRepository expiredCustomerRepository;

    private final EmailHistoryRepository emailHistoryRepository;

    private final EmailService emailService;
    private final AboutToExpireCustomerMapper aboutToExpireCustomerMapper;

    public ExpiredCustomerServiceImpl(ExpiredCustomerRepository expiredCustomerRepository, EmailHistoryRepository emailHistoryRepository, EmailService emailService, AboutToExpireCustomerMapper aboutToExpireCustomerMapper) {
        this.expiredCustomerRepository = expiredCustomerRepository;
        this.emailHistoryRepository = emailHistoryRepository;
        this.emailService = emailService;
        this.aboutToExpireCustomerMapper = aboutToExpireCustomerMapper;
    }

    @Override
    public void sendMailExpiredCustomer() {
        List<AboutToExpireCustomer> customers = expiredCustomerRepository.getCustomersHaveNotBeenEmailed();

        customers.forEach(c -> {

            Context context = new Context();

            try{
                Instant sendTime = now();
                emailService.sendHtmlMail(c.getContactEmail(), ABOUT_TO_EXPIRE_CUSTOMER, context);

                //Update about to expire customer
                c.setNotifiedAt(sendTime);
                c.setHasBeenNotified(true);
                expiredCustomerRepository.save(c);

                //Save history
                saveEmailHistory(c.getCode(), sendTime);
            } catch (MessagingException e) {
                log.info("Send email error: ", e);
            }
        });

    }

    /**
     * Save email history
     * @param customerCode customer code
     * @param sendTime send email time
     */
    private void saveEmailHistory(String customerCode, Instant sendTime){
        //Save history
        EmailHistory history = new EmailHistory();
        history.setCustomerCode(customerCode);
        history.setType(CUSTOMER);
        history.setSentBy(EMAIL_SENDER);
        history.setSentAt(sendTime);
        history.setDescription("Gửi mail");
        emailHistoryRepository.save(history);
    }

    @Override
    public void checkCustomersAboutToExpire() {
        try {
            log.info("Checking for customers are about to expire time = " + Instant.now());

            log.info("Getting customers have not been added to about to expire");
            List<AboutToExpireProjection> customersHaveNotBeenAdded =
                    expiredCustomerRepository.getCustomersThatHaveNotBeenAdded();

            log.info("Customers have not been added to about to expire size = " + customersHaveNotBeenAdded.size());

            if(nonNullOrEmpty(customersHaveNotBeenAdded)) {
                List<AboutToExpireCustomer> expireCustomerList = enrichCustomersExpireInfo(customersHaveNotBeenAdded);

                List<AboutToExpireCustomer> result = expiredCustomerRepository.saveAll(expireCustomerList);
                log.info("Inserted {} customer(s) to about to expire", result.size());
            }
        }catch (Exception e){
            log.info("An error occurred while checking for customers are about to expire, error :" + e.getMessage());
        }
    }

    private List<AboutToExpireCustomer> enrichCustomersExpireInfo(List<AboutToExpireProjection> customersHaveNotBeenAdded) {
        log.info("Enriching customers expire info");
        List<AboutToExpireCustomer> createList = aboutToExpireCustomerMapper.toEntity(customersHaveNotBeenAdded);
        createList.forEach( x ->{
         x.setStatus(ABOUT_TO_EXPIRE.code());
         x.setHasBeenNotified(Boolean.FALSE);
     });

    return createList;
    }
}
