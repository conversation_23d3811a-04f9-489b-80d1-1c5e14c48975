server:
  port: 9020

file:
  reconciliation:
    file-name:
      prefix: "VNPAY_ThongTinDoiSoat_TCTN_v2_"
      suffix: ".xlsx"

mail:
  host: email-smtp.ap-southeast-1.amazonaws.com
  port: 587
  from: <EMAIL>
  username: AKIAQNVXLONQ74ACNPOK
  passwordEncrypt: Qk5yeUNJbDROQlNYbFBiTFVTYTNlU3VITTRSNWcxQ090TGtmOTJSVkgzVmM

spring:
  kafka:
    topics:
      direct-topics-in: tvan_vnpay_in # TCT request topic
      direct-topics-out: tvan_vnpay_out # TCT response topic
    tct-direct-producer:
      bootstrap-servers: ${tct-direct-kafka}
      username: tvan-vnpay
      password: vnpay@2023
    request-consumer:
      bootstrap-servers: ${internal-kafka}
      group-id: tvan-handler
      concurrency: 5
    sync-request-consumer:
      id: syncMessageTctListener
      bootstrap-servers: ${internal-kafka}
      group-id: tvan-end-authorization
      concurrency: 5
      idle-time: 180000 #3minute in ms


scheduler:
  job:
    cron:
      end-authorize-sync-message: "0 05 17 * * *"
      daily-reconciliation-report: "0 5 7 * * *"
      remove-not-use-sync-topic: "0 0 10 * * *"
      tct-reconciliation: "0 10 7 * * *"
      send-mail-expired-customer: "0 30 13 * * *"
      callback-time: "0/20 * * ? * *"
    enable:
      end-authorize-sync-message: true
      daily-reconciliation-report: true
      remove-not-use-sync-topic: true
      tct-reconciliation: true
      send-mail-expired-customer: true
      tool-callback: false
    additional-info:
      callback-appIds: TEST1,TEST2

app:
  restTemplateTimeoutInMs: 30000

