package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.CacheService;
import vn.vnpay.tvan.apps.reconciliation.service.CallbackService;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Slf4j
@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.tool-callback", havingValue = "true")
public class CallbackInvoiceJob {
    private final CacheService cacheService;
    private final CallbackService callbackService;

    public CallbackInvoiceJob(CacheService cacheService,
                              CallbackService callbackService) {
        this.cacheService = cacheService;
        this.callbackService = callbackService;
    }

    @Scheduled(cron = "${scheduler.job.cron.callback-time}")
    public void process() {
        log.info("Callback to invoice job start");
        MDC.put(TRACE_ID, String.valueOf(System.nanoTime()));

        if (Boolean.TRUE.equals(cacheService.isAuthorized())) {
            log.info("Callback to invoice job cannot run when authorization session is ON");
        } else {
            try {
                callbackService.callbackToInvoice();
                log.info("Callback to invoice job run successfully");
            } catch (Exception ex) {
                log.error("Failed to process callback to invoice job", ex);
            } finally {
                MDC.clear();
            }
        }
    }
}
