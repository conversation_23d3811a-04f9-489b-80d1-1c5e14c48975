package vn.vnpay.tvan.libs.common.constant;

public enum InvoiceValidationError {
    ERROR_CODE_0("0", "Thành công"),
    ERROR_CODE_NOT_FOUND("-1", "Lỗi chưa xác định"),
    ERROR_CODE_NOT_SUPPORTED("-2", "Chưa hỗ trợ"),
    ERROR_CODE_CONNECT_DATABASE("-3", "Lỗi kết nối cơ sở dữ liệu"),
    ERROR_CODE_00001("00001", "Sai định dạng thông điệp"),
    ERROR_CODE_00002("00002", "Chữ ký điện tử không hợp lệ"),
    ERROR_CODE_00003("00003", "Sai thông tin mã loại thông điệp"),
    ERROR_CODE_00004("00004", "Trùng thông điệp"),
    ERROR_CODE_00005("00005", "Tổ chức TVAN chưa ký hợp đồng với TCT"),
    ERROR_CODE_00006("00006", "<PERSON><PERSON><PERSON> đồng cung cấp dịch vụ HĐĐT với TCT đã hết hiệu lực"),
    ERROR_CODE_00007("00007", "Tổ chức TVAN đang trong thời gian tạm ngừng cung cấp HĐĐT"),
    ERROR_CODE_00008("00008", "Trạng thái MST của tổ chức TVAN không hoạt động"),
    ERROR_CODE_00009("00009", "Trạng thái MST của tổ chức TVAN không hợp lệ"),
    ERROR_CODE_000010("000010", "Doanh nghiệp/Tổ chức đã hết hiệu lực kết nối trực tiếp với Tổng cục Thuế"),
    ERROR_CODE_000011("000011", "Trạng thái MST của Doanh nghiệp/Tổ chức không hoạt động"),
    ERROR_CODE_000012("000012", "TSố lượng hóa đơn không khớp giữa thông điệp và chi tiết"),
    ERROR_CODE_20000("20000", "Kí hiệu mẫu số hoá đơn không hợp lệ"),
    ERROR_CODE_20001("20001", "Sai định dạng dữ liệu"),
    ERROR_CODE_20002("20002", "Bộ MST, ký hiệu mẫu số, ký hiệu và số hóa đơn không duy nhất"),
    ERROR_CODE_20003("20003", "Hóa đơn bị thay thế không tồn tại"),
    ERROR_CODE_20004("20004", "Ký hiệu mẫu số hóa đơn không hợp lệ"),
    ERROR_CODE_20005("20005", "Ký hiệu hóa đơn không hợp lệ"),
    ERROR_CODE_20006("20006", "Ngày lập hóa đơn bị thay thế lớn hơn ngày lập hóa đơn thay thế"),
    ERROR_CODE_20007("20007", "Hóa đơn bị thay thế đã bị hủy"),
    ERROR_CODE_20008("20008", "Chỉ được thay thế cho hóa đơn gốc"),
    ERROR_CODE_20009("20009", "Không được thay thế cho hóa đơn điều chỉnh"),
    ERROR_CODE_20010("20010", "Không được thay thế cho hóa đơn bất hợp pháp"),
    ERROR_CODE_20011("20011", "Hóa đơn bị điều chỉnh không tồn tại"),
    ERROR_CODE_20012("20012", "Ký hiệu mẫu số hóa đơn không hợp lệ"),
    ERROR_CODE_20013("20013", "Ký hiệu hóa đơn không hợp lệ"),
    ERROR_CODE_20014("20014", "Ngày lập hóa đơn bị điều chỉnh lớn hơn ngày lập hóa đơn điều chỉnh"),
    ERROR_CODE_20015("20015", "Hóa đơn bị điều chỉnh đã bị hủy"),
    ERROR_CODE_20016("20016", "Hóa đơn bị điều chỉnh đã bị thay thế"),
    ERROR_CODE_20017("20017", "Không được điều chỉnh cho hóa đơn bất hợp pháp"),
    ERROR_CODE_20018("20018", "Ngày lập hóa đơn không được lớn hơn ngày hiện tại"),
    ERROR_CODE_20019("20019", "Chữ ký chưa được đăng ký"),
    ERROR_CODE_20020("20020", "Trạng thái MST tại thời điểm lập hóa đơn là không hợp lệ"),
    ERROR_CODE_20021("20021", "Hóa đơn có thời điểm lập trong thời gian NNT thuộc trường hợp ngừng sử dụng hóa đơn"),
    ERROR_CODE_20022("20022", "Loại hóa đơn không hợp lệ"),
    ERROR_CODE_20023("20023", "Hình thức áp dụng hóa đơn không hợp lệ"),
    ERROR_CODE_20024("20024", "Ký hiệu hóa đơn không phù hợp với hình thức áp dụng HĐĐT đã đăng ký"),
    ERROR_CODE_20025("20025", "MST của người mua không tồn tại"),
    ERROR_CODE_20026("20026", "Không tìm thấy thời gian ký trong chữ ký số"),
    ERROR_CODE_20027("20027", "Không tìm thấy cơ quan thuế quản lý"),
    ERROR_CODE_20028("20028", "Không được điều chỉnh cho hóa đơn thay thế"),
    ERROR_CODE_20029("20029", "Không được điều chỉnh cho hóa điều chỉnh"),
    ERROR_CODE_20030("20030", "Giá trị tổng tiền, tiền thuế tính toán không chính xác"),
    ERROR_CODE_20031("20031", "Không tìm thấy mã số thuế người mua"),
    ERROR_CODE_20032("20032", "Loại hóa đơn của hóa đơn bị thay thế và thay thế phải giống nhau"),
    ERROR_CODE_20033("20033", "Loại hóa đơn của hóa đơn bị điều chỉnh và điều chỉnh phải giống nhau"),
    ERROR_CODE_20034("20034", "Ngày lập hóa đơn bị thay thế không chính xác"),
    ERROR_CODE_20035("20035", "Ngày lập hóa đơn bị điều chỉnh không chính xác"),
    ERROR_CODE_20036("20036", "Thông tin mã số thuế người bán không khớp trong chữ ký số"),
    ERROR_CODE_20037("20037", "Thông tin mã số thuế không khớp với serial đăng ký"),
    ERROR_CODE_20038("20038", "Hóa đơn bị thay thế chưa được cấp mã hóa đơn"),
    ERROR_CODE_20039("20039", "Hóa đơn bị thay thế phải là hóa đơn có mã"),
    ERROR_CODE_20040("20040", "Hóa đơn bị điều chỉnh chưa được cấp mã hóa đơn"),
    ERROR_CODE_20041("20041", "Hóa đơn bị điều chỉnh phải là hóa đơn có mã"),
    ERROR_CODE_20042("20042", "Thông tin mã số thuế người bán không khớp với mã số thuế trong thông tin chung"),
    ERROR_CODE_20043("20043", "MST người bán trên hóa đơn không phải là MST của bên ủy nhiệm lập hóa đơn"),
    ERROR_CODE_20044("20044",
            "Hóa đơn có thời điểm lập trong thời gian bên nhận ủy nhiệm thuộc trường hợp ngừng sử dụng hóa đơn"),
    ERROR_CODE_20045("20045", "Hóa đơn có thời điểm lập không nằm trong thời hạn ủy nhiệm"),
    ERROR_CODE_20046("20046", "Chữ ký số của bên ủy nhiệm lập hóa đơn chưa đăng ký ủy nhiệm với cơ quan thuế"),
    ERROR_CODE_20047("20047", "Trạng thái MST bên nhận ủy nhiệm tại thời điểm lập hóa đơn là không hợp lệ"),
    ERROR_CODE_1("1", "Dữ liệu trong thẻ có Id = {} đã bị thay đổi trên đường truyền "),
    ERROR_CODE_2("2", "CTS không phải do nhà CA cung cấp"),
    ERROR_CODE_4("4", "CTS bị thu hồi"),
    ERROR_CODE_12("12", "Không tìm thấy chữ ký số");

    private final String code;
    private final String description;

    InvoiceValidationError(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String code() {
        return code;
    }

    public String description() {
        return description;
    }
}
