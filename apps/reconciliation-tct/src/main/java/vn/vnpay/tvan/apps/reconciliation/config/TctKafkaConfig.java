package vn.vnpay.tvan.apps.reconciliation.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class TctKafkaConfig {

    private final Map<String, Object> tctDefaultProducerConfig = Map.of(
            ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class,
            ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class,
            CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT",
            SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-256"
    );

    private ProducerFactory<String, String> producerFactory(String bootstrapAddress,
                                                            String username,
                                                            String password,
                                                            Map<String, Object> options) {
        String jaasTemplate = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";";
        String jaasCfg = String.format(jaasTemplate, username, password);
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 10);
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, false);
        props.putAll(tctDefaultProducerConfig);
        props.put(SaslConfigs.SASL_JAAS_CONFIG, jaasCfg);
        if (nonNull(options)) props.putAll(options);
        return new DefaultKafkaProducerFactory<>(
                props,
                new StringSerializer(),
                new StringSerializer()
        );
    }

    // TCT direct producer
    @Bean
    @Qualifier("tctDirectProducerFactory")
    public ProducerFactory<String, String> tctDirectProducerFactory(
            @Value(value = "${spring.kafka.tct-direct-producer.bootstrap-servers}") String servers,
            @Value(value = "${spring.kafka.tct-direct-producer.username}") String username,
            @Value(value = "${spring.kafka.tct-direct-producer.password}") String password,
            @Value("#{${spring.kafka.tct-producer.options:{}}}") Map<String, Object> options
    ) {
        return producerFactory(servers, username, password, options);
    }

    @Bean
    @Qualifier("tctDirectKafkaTemplate")
    public KafkaTemplate<String, String> tctDirectKafkaTemplate(
            @Qualifier("tctDirectProducerFactory") ProducerFactory<String, String> producerFactory
    ) {
        return new KafkaTemplate<>(producerFactory);
    }

}
