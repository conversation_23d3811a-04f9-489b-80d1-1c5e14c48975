package vn.vnpay.tvan.apps.reconciliation.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.tvan.apps.reconciliation.repository.RequestInfoRepository;
import vn.vnpay.tvan.apps.reconciliation.service.CacheService;
import vn.vnpay.tvan.apps.reconciliation.service.mapper.RequestInfoMapper;
import vn.vnpay.tvan.libs.common.constant.Constant;
import vn.vnpay.tvan.libs.common.exception.JsonException;
import vn.vnpay.tvan.libs.common.model.RequestInfo;
import vn.vnpay.tvan.libs.common.model.entity.RequestInfoEntity;
import vn.vnpay.tvan.libs.common.model.entity.TransactionError;
import vn.vnpay.tvan.libs.common.util.Utils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static vn.vnpay.tvan.libs.common.constant.Constant.DEFAULT_VALUE.AUTHORIZATION_ENABLE;
import static vn.vnpay.tvan.libs.common.constant.Constant.REDIS.AUTHORIZATION_KEY;
import static vn.vnpay.tvan.libs.common.constant.Constant.REDIS.DEFAULT_TTL_REQUEST_INFO;
import static vn.vnpay.tvan.libs.common.constant.Constant.REDIS.SYSTEM_CONFIG_HASH_SET;

@Slf4j
@Service
public class CacheServiceImpl implements CacheService {
    private final RequestInfoMapper requestInfoMapper;
    private final RequestInfoRepository requestInfoRepository;
    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper jsonMapper;

    @Value("${app.cache-request-info-to-db:false}")
    private boolean cacheRequestInfoToDb;

    public CacheServiceImpl(RequestInfoMapper requestInfoMapper,
                            RequestInfoRepository requestInfoRepository,
                            RedisTemplate<String, String> redisTemplate,
                            ObjectMapper jsonMapper) {
        this.requestInfoMapper = requestInfoMapper;
        this.requestInfoRepository = requestInfoRepository;
        this.redisTemplate = redisTemplate;
        this.jsonMapper = jsonMapper;
    }

    @Override
    public void saveRequestInfo(RequestInfo requestInfo) {
        log.info("Save request info to cache: {}", requestInfo);
        Long ttl = getTimeToLive(requestInfo.getMessageType());
        String requestInfoJson = toJson(requestInfo);
        String key = Utils.generateRequestInfoKey(requestInfo.getTransactionId());
        if (isNull(ttl) || ttl <= 0) {
            redisTemplate.opsForValue().set(key, requestInfoJson);
        } else {
            redisTemplate.opsForValue().set(key, requestInfoJson, ttl, TimeUnit.SECONDS);
        }
        log.info("Saved request info to redis");
        if (cacheRequestInfoToDb) {
            RequestInfoEntity requestInfoEntity = requestInfoMapper.toEntity(requestInfo);
            requestInfoRepository.save(requestInfoEntity);
            log.info("Saved request info to db");
        }
    }

    @Override
    public void cacheTransError(List<TransactionError> transactionErrors) {
        log.info("Save request info to cache transaction errors size = " + transactionErrors.size());
        String transToJson = toJson(transactionErrors);
        String key = transactionErrors.stream().map(TransactionError::getErrorCode).distinct().toString();
        log.info("Save request info to cache transaction errors key = " + key);


        redisTemplate.opsForValue().set(key, transToJson, 24, TimeUnit.HOURS);

        log.info("Saved request info to redis");
    }
    private Long getTimeToLive(MessageType messageType) {
        log.info("Get time to live for message type {}", messageType);
        String field = Constant.REDIS.REQUEST_INFO_TTL_PREFIX + messageType.code();
        Object ttlObject = redisTemplate.opsForHash().get(SYSTEM_CONFIG_HASH_SET, field);

        if (isNull(ttlObject)) {
            log.info("Time to live is null for message type {}", messageType);
            return null;
        } else {
            log.info("Time to live is {}", ttlObject);
            try {
                return Long.valueOf(ttlObject.toString());
            } catch (ClassCastException ex) {
                log.warn("Cannot cast time to live : {}", ttlObject);
                return DEFAULT_TTL_REQUEST_INFO;
            }
        }
    }

    @Override
    public Boolean isAuthorized() {
        Object authorizationState = redisTemplate.opsForHash().get(SYSTEM_CONFIG_HASH_SET, AUTHORIZATION_KEY);
        if (nonNull(authorizationState)) {
            return AUTHORIZATION_ENABLE.equalsIgnoreCase((String) authorizationState);
        }

        return false;
    }

    public String toJson(Object any) {
        try {
            return jsonMapper.writeValueAsString(any);
        } catch (JsonProcessingException ex) {
            throw new JsonException(ex);
        }
    }

    public int incrementCounter(String key) {
        Long result = redisTemplate.opsForValue().increment(key, 1);
        if (result == null) {
            // Key không tồn tại, tạo key và đặt giá trị là 1
            redisTemplate.opsForValue().setIfAbsent(key, "1");
            return 1;
        }
        return result.intValue();
    }

    public int incrementHashCounter(String key, String hashKey, int delta){
        Long result =  redisTemplate.opsForHash().increment(key, hashKey,delta);

        return result.intValue();
    }

    public void expire(String key){
        redisTemplate.expire(key, 0, TimeUnit.MILLISECONDS);
    }

    public Map<Object, Object> getHash(String key){
      return redisTemplate.opsForHash().entries(key);
    }
}
