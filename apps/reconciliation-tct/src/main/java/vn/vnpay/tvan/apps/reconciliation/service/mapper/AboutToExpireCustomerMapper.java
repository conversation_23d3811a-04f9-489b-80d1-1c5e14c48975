package vn.vnpay.tvan.apps.reconciliation.service.mapper;

import org.mapstruct.Mapper;
import vn.vnpay.tvan.apps.reconciliation.model.projection.AboutToExpireProjection;
import vn.vnpay.tvan.libs.common.model.entity.AboutToExpireCustomer;

import java.util.List;

@Mapper(componentModel = "spring")

public interface AboutToExpireCustomerMapper {
    List<AboutToExpireCustomer> toEntity(List<AboutToExpireProjection> projection);

    AboutToExpireCustomer toEntity(AboutToExpireProjection projection);
}
