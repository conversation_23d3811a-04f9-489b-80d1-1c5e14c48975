package vn.vnpay.tvan.apps.reconciliation.service;

import vn.vnpay.tvan.libs.common.model.RequestInfo;
import vn.vnpay.tvan.libs.common.model.entity.TransactionError;

import java.util.List;


public interface CacheService {

    /**
     * Save request info to cache
     * @param requestInfo data
     */
    void saveRequestInfo(RequestInfo requestInfo);
    void cacheTransError(List<TransactionError> transactionErrors);

    Boolean isAuthorized();
}
