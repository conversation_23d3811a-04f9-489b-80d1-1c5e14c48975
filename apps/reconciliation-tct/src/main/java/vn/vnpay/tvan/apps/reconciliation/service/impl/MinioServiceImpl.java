package vn.vnpay.tvan.apps.reconciliation.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.tvan.apps.reconciliation.service.MinioService;
import vn.vnpay.tvan.libs.common.constant.EventType;
import vn.vnpay.tvan.libs.common.exception.EndedAuthorizedException;
import vn.vnpay.tvan.libs.common.exception.JsonException;
import vn.vnpay.tvan.libs.common.model.Request;
import vn.vnpay.tvan.libs.common.model.entity.EventStore;
import vn.vnpay.tvan.libs.common.util.Utils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("MinioService")
public class MinioServiceImpl implements MinioService {

    private final MinioClient minioClient;

    private final ObjectMapper mapper;

    @Value("${minio.bucket}")
    private String bucket;

    public MinioServiceImpl(MinioClient minioClient,
                            @Qualifier("jsonMapper") ObjectMapper mapper) {
        this.minioClient = minioClient;
        this.mapper = mapper;
    }

    @Override
    public InputStream get(String key) {
        GetObjectArgs args = GetObjectArgs.builder().bucket(bucket).object(key).build();
        try {
            return minioClient.getObject(args);
        } catch (Exception e) {
            log.error("Minio cant get object with key : " + key, e);
            throw new EndedAuthorizedException("Minio cant get object with key : " + key);
        }
    }

    private String getPathByEventStoreAndEventType(EventStore eventStore, EventType eventType) {
        Instant createdTime = eventStore.getInitAt();
        String taxCode = eventStore.getTaxCode();
        MessageType messageType = MessageType.of(eventStore.getMessageType());

        //Get data để gửi thông điệp 503 -> TCT
        //Với type là RECEIVED_EINVOICE_REQUEST, điều chỉnh messageType = 200 để lấy ra đúng data
        if (EventType.RECEIVED_EINVOICE_REQUEST.equals(eventType)) {
            messageType = MessageType._200_ThongDiepGuiCapMa;
        }

        String transactionId = eventStore.getTransactionId();

        return Utils.generateMinioPath(createdTime, taxCode, eventType, messageType, transactionId);
    }

    public String getData(EventStore eventStore, EventType eventType) {
        String path = getPathByEventStoreAndEventType(eventStore, eventType);
        InputStream inputStream = get(path);

        String json = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines()
                .collect(Collectors.joining("\n"));

        JsonNode jsonNode;
        try {
            jsonNode = mapper.readTree(json);
        } catch (JsonProcessingException ex) {
            throw new JsonException(ex);
        }
        JsonNode data = jsonNode.get("data");
        return data.asText();
    }

    public Request getMessageFromMinIO(EventStore eventStore, EventType eventType) {
        String path = getPathByEventStoreAndEventType(eventStore, eventType);
        InputStream inputStream = get(path);

        String json = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines()
                .collect(Collectors.joining("\n"));

        JsonNode jsonNode;
        try {
            jsonNode = mapper.readTree(json);
        } catch (JsonProcessingException ex) {
            throw new JsonException(ex);
        }

        JsonNode data = jsonNode.get("data");
        JsonNode soLuong = jsonNode.get("soLuong");
        JsonNode mstNnt = jsonNode.get("mstNnt");

        Integer sLg;
        try {
            sLg = Integer.parseInt(soLuong.asText());
        } catch (NumberFormatException e) {
            sLg = null;
        }

        return Request.builder().mstNnt(mstNnt.asText())
                .data(data.asText())
                .soLuong(sLg)
                .build();
    }
}
