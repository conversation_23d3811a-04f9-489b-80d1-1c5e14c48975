package vn.vnpay.tvan.apps.reconciliation.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.KafkaClient;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.KafkaAdminClient;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import vn.vnpay.tvan.libs.common.deserializer.GenericJsonDeserializer;
import vn.vnpay.tvan.libs.common.model.Command;
import vn.vnpay.tvan.libs.common.model.Event;
import vn.vnpay.tvan.libs.common.model.Request;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 */
@Configuration
public class  InternalKafkaConfig {

    @Value(value = "${internal-kafka}")
    private String internalBootstrapServers;

    @Value(value = "${spring.kafka.request-consumer.group-id}")
    private String requestConsumerGroupId;

    @Value(value = "${spring.kafka.sync-request-consumer.bootstrap-servers}")
    private String endAuthorizationBootstrapServers;

    @Value(value = "${spring.kafka.sync-request-consumer.group-id}")
    private String endAuthorizationConsumerGroupId;

    @Value(value = "${spring.kafka.sync-request-consumer.idle-time}")
    private Long syncMessagesTctIdleTime;

    @Bean
    @Qualifier("requestConsumerFactory")
    public ConsumerFactory<String, Command<Request>> requestConsumerFactory(
            @Qualifier("jsonMapper") ObjectMapper jsonMapper,
            @Value("#{${spring.kafka.internal-consumer.options:{}}}") Map<String, Object> options
    ) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, internalBootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, requestConsumerGroupId);
        props.put(JsonDeserializer.TRUSTED_PACKAGES, "*");
        if (nonNull(options)) props.putAll(options);
        TypeReference<Command<Request>> typeReference = new TypeReference<>() {};
        return new DefaultKafkaConsumerFactory<>(
                props,
                new StringDeserializer(),
                new GenericJsonDeserializer<>(jsonMapper, typeReference)
        );
    }

    @Bean
    @Qualifier("requestCommandListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, Command<Request>> requestCommandListenerContainerFactory(
            @Qualifier("requestConsumerFactory") ConsumerFactory<String, Command<Request>> requestConsumerFactory
    ) {
        var factory = new ConcurrentKafkaListenerContainerFactory<String, Command<Request>>();
        factory.setConsumerFactory(requestConsumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }


    @Bean
    @Qualifier("internalConsumerFactory")
    public ConsumerFactory<String, String> internalConsumerFactory(
            @Value("#{${spring.kafka.internal-consumer.options:{}}}") Map<String, Object> options
    ) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, internalBootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, requestConsumerGroupId);
        if (nonNull(options)) props.putAll(options);
        return new DefaultKafkaConsumerFactory<>(
                props,
                new StringDeserializer(),
                new StringDeserializer()
        );
    }

    @Bean
    @Qualifier("internalListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> internalListenerContainerFactory(
            @Qualifier("internalConsumerFactory") ConsumerFactory<String, String> internalConsumerFactory
    ) {
        var factory = new ConcurrentKafkaListenerContainerFactory<String, String>();
        factory.setConsumerFactory(internalConsumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }

    @Bean
    @Qualifier("internalProducerFactory")
    public ProducerFactory<String, Object> internalProducerFactory(
            @Value("#{${spring.kafka.internal-producer.options:{}}}") Map<String, Object> options
    ) {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, internalBootstrapServers);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 10);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        if (nonNull(options)) props.putAll(options);
        return new DefaultKafkaProducerFactory<>(props);
    }

    @Bean
    @Qualifier("internalKafkaTemplate")
    public KafkaTemplate<String, Object> internalKafkaTemplate(
            @Qualifier("internalProducerFactory") ProducerFactory<String, Object> producerFactory
    ) {
        return new KafkaTemplate<>(producerFactory);
    }

    /*--------------------------------------------------------------------------------------------------------*/
    /*
    Sync-messages-TCT-listener config
    Consume thực hiện lắng nghe event, với mỗi event sẽ gửi 1 messages đối soát đến TCT
     */

    @Bean
    @Qualifier("endAuthorizationProcessRequestConsumerFactory")
    public ConsumerFactory<String, Event<Object>> endAuthorizationProcessRequestConsumerFactory(
            @Qualifier("jsonMapper") ObjectMapper jsonMapper,
            @Value("#{${spring.kafka.internal-consumer.options:{}}}") Map<String, Object> options
    ) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, endAuthorizationBootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, endAuthorizationConsumerGroupId);
        props.put(JsonDeserializer.TRUSTED_PACKAGES, "*");
        if (nonNull(options)) props.putAll(options);
        return new DefaultKafkaConsumerFactory<>(
                props,
                new StringDeserializer(),
                new GenericJsonDeserializer<>(jsonMapper, new TypeReference<>() {})
        );
    }

    @Bean
    @Qualifier("endAuthorizationProcessRequestListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, Event<Object>> endAuthorizationProcessRequestListenerContainerFactory(
            @Qualifier("endAuthorizationProcessRequestConsumerFactory") ConsumerFactory<String, Event<Object>> requestConsumerFactory
    ) {
        var factory = new ConcurrentKafkaListenerContainerFactory<String, Event<Object>>();
        factory.setConsumerFactory(requestConsumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.getContainerProperties().setIdleEventInterval(syncMessagesTctIdleTime);
        return factory;
    }

    /*
    Sync-messages-TCT-listener config end
     */
    /*--------------------------------------------------------------------------------------------------------*/

    @Bean
    @Qualifier("kafkaClient")
    public AdminClient kafkaClient(){
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, internalBootstrapServers);

        return KafkaAdminClient.create(props);
    }
}
