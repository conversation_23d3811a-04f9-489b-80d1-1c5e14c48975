# Nexus repository credentials
# Replace with actual credentials or use environment variables
NEXUS_USER=your_nexus_username
NEXUS_PASSWORD=your_nexus_password

# Gradle configuration
org.gradle.jvmargs=-Xmx2048m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Dcom.sun.net.ssl.checkRevocation=false -Dtrust_all_cert=true
org.gradle.parallel=true
org.gradle.caching=true

# Network configuration - uncomment and configure if behind proxy
# systemProp.http.proxyHost=your.proxy.host
# systemProp.http.proxyPort=8080
# systemProp.https.proxyHost=your.proxy.host
# systemProp.https.proxyPort=8080
# systemProp.http.proxyUser=username
# systemProp.http.proxyPassword=password
# systemProp.https.proxyUser=username
# systemProp.https.proxyPassword=password

# SSL configuration
systemProp.https.protocols=TLSv1.2,TLSv1.3
systemProp.javax.net.ssl.trustStoreType=JKS
