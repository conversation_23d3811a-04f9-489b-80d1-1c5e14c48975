package vn.vnpay.tvan.apps.reconciliation.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SummaryDTO {
    private int s100;
    private int s100r1;
    private int s100r999;
    private int s100r102;
    private int s100r103;
    private int s200;
    private int s200r1;
    private int s200r999;
    private int s200r202;
    private int s200r204;
    private int s203;
    private int s203r1;
    private int s203r999;
    private int s203r204;
    private int s300;
    private int s300r1;
    private int s300r999;
    private int s300r204;
    private int s300r301;
    private int s400;
    private int s400r1;
    private int s400r999;
    private int s400r204;

}
