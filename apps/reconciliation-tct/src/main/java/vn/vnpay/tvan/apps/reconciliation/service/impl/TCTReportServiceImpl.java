package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jxls.common.Context;
import org.jxls.transform.Transformer;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.tvan.apps.reconciliation.model.TotalMessageReport;
import vn.vnpay.tvan.apps.reconciliation.repository.MissingMessageRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.TotalMessageReportRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.model.MissingMessageDTO;
import vn.vnpay.tvan.apps.reconciliation.service.EmailService;
import vn.vnpay.tvan.apps.reconciliation.service.TCTReportService;
import vn.vnpay.tvan.apps.reconciliation.service.dto.MissingMessageSummaryDTO;
import vn.vnpay.tvan.apps.reconciliation.service.dto.SummaryDTO;
import vn.vnpay.tvan.apps.reconciliation.service.mapper.SummaryMapper;

import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static vn.vnpay.share.message.constant.MessageType._102_ThongDiepThongBaoTiepNhanToKhai;
import static vn.vnpay.share.message.constant.MessageType._103_ThongDiepThongBaoChapNhanToKhai;
import static vn.vnpay.share.message.constant.MessageType._1_Loi;
import static vn.vnpay.share.message.constant.MessageType._202_ThongDiepThongBaoKetQuaCapMa;
import static vn.vnpay.share.message.constant.MessageType._204_ThongDiepThongBaoKetQuaKiemTraDuLieu;
import static vn.vnpay.share.message.constant.MessageType._301_ThongDiepThongBaoTiepNhanXuLyHoaDonSaiSot;
import static vn.vnpay.share.message.constant.MessageType._999_ThongDiepPhanHoiKyThuat;


@Slf4j
@Service
public class TCTReportServiceImpl implements TCTReportService {


    private final EmailService emailService;
    private final SummaryMapper summaryMapper;
    private final TotalMessageReportRepository totalMessageReportRepository;
    private final MissingMessageRepository missingMessageRepository;

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyy");

    private static final String CONTENT_TYPE = "application/vnd.ms-excel";

    @Value("${file.reconciliation.file-name.prefix}")
    private String fileNamePrefix;
    @Value("${file.reconciliation.file-name.suffix}")
    private String fileNameSuffix;

    public TCTReportServiceImpl(EmailService emailService,
                                SummaryMapper summaryMapper,
                                TotalMessageReportRepository totalMessageReportRepository,
                                MissingMessageRepository missingMessageRepository) {
        this.emailService = emailService;
        this.summaryMapper = summaryMapper;
        this.totalMessageReportRepository = totalMessageReportRepository;
        this.missingMessageRepository = missingMessageRepository;
    }


    @Override
    public void generateTCTReport() {
        //create file excel timme
        String now = LocalDateTime.now().format(dateTimeFormatter);

        //Total message by day
        String reportDate = LocalDateTime.now().minusDays(1).format(dateTimeFormatter);
        List<TotalMessageReport> totalMessageReports = totalMessageReportRepository.getByReportDate(reportDate);
        if (totalMessageReports.isEmpty()) {
            log.error("TotalMessageReport not found: reportDate {}", reportDate);
            return;
        }
        SummaryDTO daily = getTotalMessageSummary(totalMessageReports);

        //Cumulative total message
        SummaryDTO total = summaryMapper.from(totalMessageReportRepository.getCumulativeTotalReport());

        //Miss message
        List<MissingMessageDTO> missingMessages = missingMessageRepository.getAllMissingMessageByTypeBefore7h();
        MissingMessageSummaryDTO missing = getMissingMessageSummary(missingMessages);

        log.info("Generate TCT report for {}", now);
        log.info("Daily summary: {}", daily);
        log.info("Total summary: {}", total);

        try {
            ByteArrayOutputStream outputStream = generateTCTReport(daily, total, now, missing);
            ByteArrayDataSource dataSource = new ByteArrayDataSource(outputStream.toByteArray(), CONTENT_TYPE);
            String fileName =
                    MessageFormat.format("{0}{1}{2}", fileNamePrefix, now.replace("/", ""), fileNameSuffix);
            boolean result = emailService.sendMailWithAttachment(fileName, dataSource);
            log.info("Send TCT report result: {}", result);
        } catch (IOException ex) {
            log.error("Error while generate report", ex);
        }

    }

    private MissingMessageSummaryDTO getMissingMessageSummary(List<MissingMessageDTO> missingMessages){
        MissingMessageSummaryDTO summary = new MissingMessageSummaryDTO();

        if (isNull(missingMessages)) return summary;

        record Type(Integer requestType, Integer expectedType) {}

        Map<Type, List<String>> map = new HashMap<>();

        missingMessages.forEach(m -> map
                .computeIfAbsent(
                        new Type(m.getRequestType(), m.getExpectedType()),
                        k -> new ArrayList<>()
                )
                .add(m.getId()));

        map.forEach((type, listId) -> {
            setFieldOfMissingMessageSummary(summary, type.requestType, type.expectedType, listId);
        });

        return summary;
    }

    private void setFieldOfMissingMessageSummary(MissingMessageSummaryDTO summary,
                                          Integer requestTypeCode,
                                          Integer expectedTypeCode,
                                          List<String> listId){

        log.info("Missing message: requestType = {}, expectedType = {}, numberOfTransactions = {}",
                requestTypeCode, expectedTypeCode, listId.size());

        StringBuilder listIdStrBuilder = new StringBuilder();
        listId.forEach(id -> listIdStrBuilder.append(id).append(","));

        /**
         * The maximum length of cell contents (text) is 32767
         * 32560 = 740 * 44 Contain 740 transactionId (length of one transactionId = 44)
         */
        final int maxLengthOfCell = 32560;
        List<String> listIdStr = splitStringByLength(listIdStrBuilder.toString(), maxLengthOfCell);

        MessageType requestType = MessageType.of(requestTypeCode);
        MessageType expectedType = MessageType.of(expectedTypeCode);

        switch (requestType) {
            case _100_ThongDiepGuiToKhai:
                if(expectedType == _999_ThongDiepPhanHoiKyThuat) summary.setS100m999s(listIdStr);
                else if(expectedType == _102_ThongDiepThongBaoTiepNhanToKhai) summary.setS100m102s(listIdStr);
                else if(expectedType == _103_ThongDiepThongBaoChapNhanToKhai) summary.setS100m103s(listIdStr);
                break;
            case _200_ThongDiepGuiCapMa:
                if(expectedType == _999_ThongDiepPhanHoiKyThuat) summary.setS200m999s(listIdStr);
                else if(expectedType == _202_ThongDiepThongBaoKetQuaCapMa) summary.setS200m202s(listIdStr);
                else if(expectedType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS200m204s(listIdStr);
                break;
            case _203_ThongDiepChuyenDuLieuKhongMa:
                if(expectedType == _999_ThongDiepPhanHoiKyThuat) summary.setS203m999s(listIdStr);
                else if(expectedType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS203m204s(listIdStr);
                break;
            case _206_ThongDiepGuiCapMaHoaDonTaoTuMayTinhTien:
                if(expectedType == _999_ThongDiepPhanHoiKyThuat) summary.setS206m999s(listIdStr);
                else if(expectedType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS206m204s(listIdStr);
                break;
            case _300_ThongDiepThongBaoHoaDonSaiSot, _303_ThongDiepThongBaoHoaDonSaiSotTuMayTinhTien:
                if(expectedType == _999_ThongDiepPhanHoiKyThuat) summary.setS300m999s(listIdStr);
                else if(expectedType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS300m204s(listIdStr);
                else if(expectedType == _301_ThongDiepThongBaoTiepNhanXuLyHoaDonSaiSot) summary.setS300m301s(listIdStr);
                break;
            case _400_ThongDiepChuyenBangTongHopDuLieu:
                if(expectedType == _999_ThongDiepPhanHoiKyThuat) summary.setS400m999s(listIdStr);
                else if(expectedType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS400m204s(listIdStr);
                break;
            default:
                log.info("Invalid request type: {}", requestType);
        }
    }

    /**
     * Split String by max length
     * @param str
     * @param maxLength
     * @return list String after split
     */
    public List<String> splitStringByLength(String str, int maxLength){
        List<String> result = new ArrayList<>();

        if(str.length() <= maxLength){
            result.add(str);
            return result;
        }

        int beginIndex = 0;
        int endIndex = maxLength;
        while (str.length() > endIndex){
            result.add(str.substring(beginIndex, endIndex));
            beginIndex += maxLength;
            endIndex += maxLength;
        }
        result.add(str.substring(beginIndex));

        return result;
    }

    private SummaryDTO getTotalMessageSummary(List<TotalMessageReport> totalMessageReports){
        SummaryDTO summary = new SummaryDTO();

        totalMessageReports.forEach(totalMessageReport -> {
            log.info("Daily message report: reportDate = {}, requestType = {}, responseType = {}, total = {}",
                    totalMessageReport.getReportDate(),totalMessageReport.getRequestType(),
                    totalMessageReport.getResponseType(), totalMessageReport.getTotal());

            MessageType requestType = MessageType.of(totalMessageReport.getRequestType());
            MessageType responseType = MessageType.of(totalMessageReport.getResponseType());

            Integer total = totalMessageReport.getTotal();
            switch (requestType) {
                case _100_ThongDiepGuiToKhai:
                    if(isNull(responseType)) summary.setS100(total);
                    else if(responseType == _1_Loi) summary.setS100r1(total);
                    else if(responseType == _999_ThongDiepPhanHoiKyThuat) summary.setS100r999(total);
                    else if(responseType == _102_ThongDiepThongBaoTiepNhanToKhai) summary.setS100r102(total);
                    else if(responseType == _103_ThongDiepThongBaoChapNhanToKhai) summary.setS100r103(total);
                    break;
                case _200_ThongDiepGuiCapMa:
                    if(isNull(responseType)) summary.setS200(total);
                    else if(responseType == _1_Loi) summary.setS200r1(total);
                    else if(responseType == _999_ThongDiepPhanHoiKyThuat) summary.setS200r999(total);
                    else if(responseType == _202_ThongDiepThongBaoKetQuaCapMa) summary.setS200r202(total);
                    else if(responseType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS200r204(total);
                    break;
                case _203_ThongDiepChuyenDuLieuKhongMa:
                    if(isNull(responseType)) summary.setS203(total);
                    else if(responseType == _1_Loi) summary.setS203r1(total);
                    else if(responseType == _999_ThongDiepPhanHoiKyThuat) summary.setS203r999(total);
                    else if(responseType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS203r204(total);
                    break;
                case _300_ThongDiepThongBaoHoaDonSaiSot, _303_ThongDiepThongBaoHoaDonSaiSotTuMayTinhTien:
                    if(isNull(responseType)) summary.setS300(total);
                    else if(responseType == _1_Loi) summary.setS300r1(total);
                    else if(responseType == _999_ThongDiepPhanHoiKyThuat) summary.setS300r999(total);
                    else if(responseType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS300r204(total);
                    else if(responseType == _301_ThongDiepThongBaoTiepNhanXuLyHoaDonSaiSot) summary.setS300r301(total);
                    break;
                case _400_ThongDiepChuyenBangTongHopDuLieu:
                    if(isNull(responseType)) summary.setS400(total);
                    else if(responseType == _1_Loi) summary.setS400r1(total);
                    else if(responseType == _999_ThongDiepPhanHoiKyThuat) summary.setS400r999(total);
                    else if(responseType == _204_ThongDiepThongBaoKetQuaKiemTraDuLieu) summary.setS400r204(total);
                    break;
                default:
                    log.info("Invalid request type: {}", requestType);
            }
        });

        return summary;
    }


    private ByteArrayOutputStream generateTCTReport(SummaryDTO daily,
                                                    SummaryDTO total,
                                                    String reportDate,
                                                    MissingMessageSummaryDTO missing) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try (InputStream is = new FileInputStream("./templates/TCTReport.xlsx")) {
            Context context = new Context();
            context.putVar("reportDate", reportDate);
            context.putVar("daily", daily);
            context.putVar("total", total);
            context.putVar("missing", missing);

            Transformer transformer = JxlsHelper.getInstance().createTransformer(is, os);
            transformer.setEvaluateFormulas(true);
            JxlsHelper.getInstance().processTemplate(context, transformer);
        }
        return os;
    }
}
