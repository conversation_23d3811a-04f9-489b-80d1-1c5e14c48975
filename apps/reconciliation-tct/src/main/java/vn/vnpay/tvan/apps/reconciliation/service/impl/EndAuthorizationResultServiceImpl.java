package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.vnpay.tvan.apps.reconciliation.model.AuthorizeReconciliationResultModel;
import vn.vnpay.tvan.apps.reconciliation.model.EndAuthorizationProjection;
import vn.vnpay.tvan.apps.reconciliation.repository.EndAuthorizationEventStoreRepository;
import vn.vnpay.tvan.apps.reconciliation.service.EndAuthorizationResultService;
import vn.vnpay.tvan.libs.common.constant.Constant;

import java.util.List;

@Slf4j
@Service("EndAuthorizationResultService")
public class EndAuthorizationResultServiceImpl implements EndAuthorizationResultService {

    private final EndAuthorizationEventStoreRepository repository;

    public EndAuthorizationResultServiceImpl(EndAuthorizationEventStoreRepository repository) {
        this.repository = repository;
    }

    @Override
    public AuthorizeReconciliationResultModel getDataForReconciliation(String sessionId) {
        log.info("Get data for reconciliation start");
        int sumTotalMessage500 = 0;
        int sumTotalMessage503 = 0;
        int sumTotalMessage504 = 0;
        int sumTotalMessage999Of500Success = 0;
        int sumTotalMessage999Of503Success = 0;
        int sumTotalMessage999Of504Success = 0;
        int sumTotalMessage999Of500Error = 0;
        int sumTotalMessage999Of503Error = 0;
        int sumTotalMessage999Of504Error = 0;
        int sumTotalMessage1Of500 = 0;
        int sumTotalMessage1Of503 = 0;
        int sumTotalMessage1Of504 = 0;

        List<EndAuthorizationProjection> totalMessageList = repository.countTotalMessageForReconciliation(sessionId);
        if (!CollectionUtils.isEmpty(totalMessageList)) {
            for(EndAuthorizationProjection totalMessage : totalMessageList){
                if(Constant.AUTHORIZATION_SESSION.MESSAGE_500_INT.equals(totalMessage.getMessageType())){
                    sumTotalMessage999Of500Success = totalMessage.getTotal999Success();
                    sumTotalMessage999Of500Error = totalMessage.getTotal999Error();
                    sumTotalMessage1Of500 = totalMessage.getTotal1();
                    sumTotalMessage500 = totalMessage.getTotalMissing() + sumTotalMessage999Of500Success +
                            sumTotalMessage999Of500Error + sumTotalMessage1Of500;
                } else if(Constant.AUTHORIZATION_SESSION.MESSAGE_503_INT.equals(totalMessage.getMessageType())){
                    sumTotalMessage999Of503Success = totalMessage.getTotal999Success();
                    sumTotalMessage999Of503Error = totalMessage.getTotal999Error();
                    sumTotalMessage1Of503 = totalMessage.getTotal1();
                    sumTotalMessage503 = totalMessage.getTotalMissing() + sumTotalMessage999Of503Success +
                            sumTotalMessage999Of503Error + sumTotalMessage1Of503;
                } else if(Constant.AUTHORIZATION_SESSION.MESSAGE_504_INT.equals(totalMessage.getMessageType())){
                    sumTotalMessage999Of504Success = totalMessage.getTotal999Success();
                    sumTotalMessage999Of504Error = totalMessage.getTotal999Error();
                    sumTotalMessage1Of504 = totalMessage.getTotal1();
                    sumTotalMessage504 = totalMessage.getTotalMissing() + sumTotalMessage999Of504Success +
                            sumTotalMessage999Of504Error + sumTotalMessage1Of504;
                }
            }
        }
        return AuthorizeReconciliationResultModel.builder().sumTotalMessage500(sumTotalMessage500)
                .sumTotalMessage503(sumTotalMessage503).sumTotalMessage504(sumTotalMessage504)
                .sumTotalMessage999Of500Success(sumTotalMessage999Of500Success)
                .sumTotalMessage999Of503Success(sumTotalMessage999Of503Success)
                .sumTotalMessage999Of504Success(sumTotalMessage999Of504Success)
                .sumTotalMessage999Of500Error(sumTotalMessage999Of500Error)
                .sumTotalMessage999Of503Error(sumTotalMessage999Of503Error)
                .sumTotalMessage999Of504Error(sumTotalMessage999Of504Error)
                .sumTotalMessage1Of500(sumTotalMessage1Of500).sumTotalMessage1Of503(sumTotalMessage1Of503)
                .sumTotalMessage1Of504(sumTotalMessage1Of504).build();
    }
}
