database-url: *****************************************
internal-kafka: localhost:9093
tct-direct-kafka: localhost:9094
tct-authorization-kafka: localhost:9094
minio-url: http://127.0.0.1:9000
minio-access-key: minioadmin
minio-secret-key: minio@123
minio-bucket: etl

spring:
  redis:
    host: ************
    port: 6379
    password: vn<PERSON><PERSON><PERSON>@123
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  kafka:
    topics:
      request-topics: dev-request # Internal request topic
      event-topics: dev-event # Internal event topic
      init-taxcode-topics: dev-init-taxcode # Internal topic for taxcode init request
      sync-request-topics: dev-sync-request #các thông điệp 202 và 204 sẽ đồng thời đc push vào topic này, để phục v<PERSON> việc sync với TCT sau khi kết thúc <PERSON>y quyền
      wakeup-sync-request-topics: dev-wakeup-sync-request #có event trong topic này thì sẽ wakeup listener nào lắng nghe sync-request-topics
    event-consumer:
      concurrency: 2
    request-consumer:
      concurrency: 2
    tct-direct-consumer:
      concurrency: 2
    tct-authorization-consumer:
      concurrency: 2
    tct-tax-code-consumer:
      concurrency: 2
    sync-request-consumer:
      concurrency: 2
    wakeup-sync-request-consumer:
      concurrency: 2
