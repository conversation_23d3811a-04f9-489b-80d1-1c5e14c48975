package vn.vnpay.tvan.apps.reconciliation.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.share.message.reconciliation.DLieu901;
import vn.vnpay.share.message.reconciliation.DSPHoi;
import vn.vnpay.share.message.reconciliation.SLDSoat;
import vn.vnpay.share.message.reconciliation.SLNCTiet;
import vn.vnpay.share.message.reconciliation.TDiep901;
import vn.vnpay.share.message.reconciliation.TTChung;
import vn.vnpay.tvan.apps.reconciliation.model.EventStoreProjection;
import vn.vnpay.tvan.apps.reconciliation.repository.DailyMessageReportTctRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.DailyReportStatusRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.DataReportHistoryRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.DifferentMessageReportRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.EventStoreRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.FailedMessageReportRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.MissingMessageRepository;
import vn.vnpay.tvan.apps.reconciliation.service.CacheService;
import vn.vnpay.tvan.apps.reconciliation.service.DailyReportService;
import vn.vnpay.tvan.apps.reconciliation.service.EventService;
import vn.vnpay.tvan.libs.common.constant.EventType;
import vn.vnpay.tvan.libs.common.constant.MessageStatus;
import vn.vnpay.tvan.libs.common.constant.ReconciliationMessageType;
import vn.vnpay.tvan.libs.common.constant.ReportStatus;
import vn.vnpay.tvan.libs.common.model.Event;
import vn.vnpay.tvan.libs.common.model.RequestInfo;
import vn.vnpay.tvan.libs.common.model.entity.DailyMessageReportTct;
import vn.vnpay.tvan.libs.common.model.entity.DailyReportStatus;
import vn.vnpay.tvan.libs.common.model.entity.DataReportHistory;
import vn.vnpay.tvan.libs.common.model.entity.DifferentMessageReport;
import vn.vnpay.tvan.libs.common.model.entity.FailedMessageReport;
import vn.vnpay.tvan.libs.common.model.entity.MissingMessage;
import vn.vnpay.tvan.libs.common.util.MdcListenableFutureCallback;
import vn.vnpay.tvan.libs.common.util.Utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static vn.vnpay.tvan.libs.common.constant.Constant.DATE_FORMAT.DATE_ONLY_FORMATTER;
import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRANSACTION_ID;

@Slf4j
@Service
public class DailyReportServiceImpl implements DailyReportService {
    @Value("${app.taxCode}")
    private String tvanTaxCode;

    @Value("${app.pban}")
    private String pBan;

    @Value("${app.tctTaxCode}")
    private String tctTaxCode;
    @Value("${spring.kafka.topics.direct-topics-in}")
    private String kafkaQueueIn;
    private final DailyMessageReportTctRepository dailyMessageReportTctRepository;
    private final ObjectMapper xmlMapper = new XmlMapper().registerModule(new JaxbAnnotationModule());
    private final MissingMessageRepository missingMessageRepository;
    private final KafkaTemplate<String, String> tctKafkaTemplate;
    private final CacheService cacheService;
    private final EventService eventService;
    private final DailyReportStatusRepository dailyReportStatusRepository;
    private final DifferentMessageReportRepository differentMessageReportRepository;
    private final DataReportHistoryRepository dataReportHistoryRepository;
    private final EventStoreRepository eventStoreRepository;
    private final FailedMessageReportRepository failedMessageReportRepository;


    public DailyReportServiceImpl(DailyMessageReportTctRepository dailyMessageReportTctRepository,
                                  MissingMessageRepository missingMessageRepository,
                                  @Qualifier("tctDirectKafkaTemplate") KafkaTemplate<String, String> tctKafkaTemplate,
                                  CacheService cacheService, EventService eventService, DailyReportStatusRepository dailyReportStatusRepository,
                                  DifferentMessageReportRepository differentMessageReportRepository,
                                  DataReportHistoryRepository dataReportHistoryRepository,
                                  EventStoreRepository eventStoreRepository,
                                  FailedMessageReportRepository failedMessageReportRepository) {
        this.dailyMessageReportTctRepository = dailyMessageReportTctRepository;
        this.missingMessageRepository = missingMessageRepository;
        this.tctKafkaTemplate = tctKafkaTemplate;
        this.cacheService = cacheService;
        this.eventService = eventService;
        this.dailyReportStatusRepository = dailyReportStatusRepository;
        this.differentMessageReportRepository = differentMessageReportRepository;
        this.dataReportHistoryRepository = dataReportHistoryRepository;
        this.eventStoreRepository = eventStoreRepository;
        this.failedMessageReportRepository = failedMessageReportRepository;
    }

    @Override
    public void sendDailyMessageReportToTCT() throws JsonProcessingException, NullPointerException {
        log.info("Start send daily message report to TCT");
        List<DailyReportStatus> dailyReportStatuses =
                dailyReportStatusRepository.findByStatus(ReportStatus.UNSENT.value());

        for (DailyReportStatus dailyReportStatus : dailyReportStatuses) {
            processDailyMessageReport(dailyReportStatus);
        }

    }

    private void processDailyMessageReport(DailyReportStatus dailyReportStatus) throws JsonProcessingException {
        List<MissingMessage> summaryMissingMessage = new ArrayList<>();
        List<SLDSoat> data = new ArrayList<>();
        LocalDate reportDate = dailyReportStatus.getReportDate();
        List<DailyMessageReportTct> dailyMessageReportTctList = getListDailyMessageReportTct(reportDate);
        String mTDiep = tvanTaxCode + Utils.upperCaseUUIDWithoutDash();
        MDC.put(TRANSACTION_ID, mTDiep);
        String reportDocuments = createReportDocuments(dailyMessageReportTctList, mTDiep, summaryMissingMessage,
                data, reportDate);
        log.info("Created message 901 with id: " + mTDiep);
        sendReportDocumentsToTCT(reportDocuments, mTDiep);

        dailyReportStatus.setReportTime(Instant.now());
        dailyReportStatus.setStatus(ReportStatus.SENT.value());
        dailyReportStatus.setMessageId(mTDiep);
        saveDailyReportStatus(dailyReportStatus);
        //save data report history
        saveDailyReport(data, mTDiep, reportDate);
        // save different message report
        saveDifferentMessageReport(summaryMissingMessage, mTDiep, reportDate);
        //save report details
        saveReportDetails(mTDiep, reportDate);
        log.info("Save daily report date {} successfully", reportDate);
    }

    private void saveReportDetails(String mTDiep, LocalDate reportDate) {
        Instant yesterday = Instant.from(reportDate.atStartOfDay(ZoneOffset.UTC));
        Instant today = yesterday.plus(1, ChronoUnit.DAYS);
        List<EventStoreProjection> eventStoreProjections =
                eventStoreRepository.findNotSuccessEvents(yesterday, today);
        List<FailedMessageReport> notSuccessMessageReports = new ArrayList<>();
        eventStoreProjections.forEach(e -> notSuccessMessageReports.add(FailedMessageReport.builder()
                .reportMessageId(mTDiep)
                .messageId(e.getMessageId())
                .messageType(e.getReferenceMessageType())
                .responseType(e.getMessageType())
                .taxCode(e.getTaxCode())
                .status(getDetailMessageStatus(e.getMessageType(), e.getReferenceMessageType()))
                .reportDate(reportDate).build()));
        failedMessageReportRepository.saveAll(notSuccessMessageReports);
        log.info("Saved {} report details successfully", notSuccessMessageReports.size());
    }

    private Integer getDetailMessageStatus(Integer messageType, Integer refType) {
        if (messageType == -1) {
            return MessageStatus.ERROR.getValue();
        }

        if (messageType == 102 || (messageType == 204 && refType == 300) || (messageType == 999)) {
            return MessageStatus.NOT_RECEIVED.getValue();
        }

        if (messageType == 103 || (messageType == 204 && (refType == 200 || refType == 201 ||
                refType == 400)) || messageType == 301) {
            return MessageStatus.NOT_ACCEPTED.getValue();
        }

        return MessageStatus.UNKNOWN.getValue();
    }

    private void saveDailyReport(List<SLDSoat> data, String mTDiep, LocalDate reportDate) {
        List<DataReportHistory> dataReportHistories = new ArrayList<>();
        for (SLDSoat sldSoat : data) {
            if (sldSoat.getDSPHLech() != null) {
                sldSoat.getDSPHLech().forEach(dsp -> dataReportHistories.add(DataReportHistory.builder()
                        .reportDate(reportDate)
                        .reportMessageId(mTDiep)
                        .messageType(Integer.valueOf(sldSoat.getMLTDiep()))
                        .totalSent(sldSoat.getSLDGui())
                        .responseType(dsp.getMLPHoi())
                        .totalReceived(dsp.getSLGNhan())
                        .totalDeviation(dsp.getSLGLech()).build()));
            }
        }
        dataReportHistoryRepository.saveAll(dataReportHistories);
        log.info("Saved data report history successfully");
    }

    private void saveDifferentMessageReport(List<MissingMessage> summaryMissingMessage, String mTDiep, LocalDate reportDate) {
        List<DifferentMessageReport> differentMessageReports = new ArrayList<>();
        summaryMissingMessage.forEach(missingMessage -> differentMessageReports.add(DifferentMessageReport.builder()
                .missingMessageId(missingMessage.getId())
                .messageType(missingMessage.getRequestType())
                        .receivedType(missingMessage.getExpectedType())
                .taxCode(missingMessage.getTaxCode())
                .reportMessageId(mTDiep)
                .reportDate(reportDate)
                .build()));
        differentMessageReportRepository.saveAll(differentMessageReports);
        log.info("Saved {} different message report successfully", differentMessageReports.size());
    }

    @Override
    public void saveDailyReportStatus(DailyReportStatus dailyReportStatus) {
        dailyReportStatusRepository.save(dailyReportStatus);
        log.info("Save daily report history with id: {} successfully", dailyReportStatus.getMessageId());
    }

    private List<DailyMessageReportTct> getListDailyMessageReportTct(LocalDate date) {
        return dailyMessageReportTctRepository.findAllByReportDate(date);
    }

    private String createReportDocuments(List<DailyMessageReportTct> listDailyMessageReportTct, String mTDiep,
                                         List<MissingMessage> summaryMissingMessage, List<SLDSoat> data, LocalDate reportDate)
            throws JsonProcessingException, NullPointerException{

        List<DailyMessageReportTct> listDailyMessage100 = listDailyMessageReportTct.stream()
                .filter(d -> d.getMessageType() == 100).toList();
        List<DailyMessageReportTct> listDailyMessage200 = listDailyMessageReportTct.stream()
                .filter(d -> d.getMessageType() == 200).toList();
        List<DailyMessageReportTct> listDailyMessage101 = listDailyMessageReportTct.stream()
                .filter(d -> d.getMessageType() == 101).toList();
        List<DailyMessageReportTct> listDailyMessage201 = listDailyMessageReportTct.stream()
                .filter(d -> d.getMessageType() == 201).toList();
        List<DailyMessageReportTct> listDailyMessage203 = listDailyMessageReportTct.stream()
                .filter(d -> d.getMessageType() == 203).toList();
        List<DailyMessageReportTct> listDailyMessage300 = listDailyMessageReportTct.stream()
                .filter(d -> d.getMessageType() == 300).toList();
        List<DailyMessageReportTct> listDailyMessage400 = listDailyMessageReportTct.stream()
                .filter(d -> d.getMessageType() == 400).toList();

        SLDSoat sldSoat100 = creatSLDSoat(listDailyMessage100, 100, summaryMissingMessage, reportDate);
        SLDSoat sldSoat200 = creatSLDSoat(listDailyMessage200, 200, summaryMissingMessage, reportDate);
        SLDSoat sldSoat101 = creatSLDSoat(listDailyMessage101, 101, summaryMissingMessage, reportDate);
        SLDSoat sldSoat201 = creatSLDSoat(listDailyMessage201, 201, summaryMissingMessage, reportDate);
        SLDSoat sldSoat203 = creatSLDSoat(listDailyMessage203, 203, summaryMissingMessage, reportDate);
        SLDSoat sldSoat300 = creatSLDSoat(listDailyMessage300, 300, summaryMissingMessage, reportDate);
        SLDSoat sldSoat400 = creatSLDSoat(listDailyMessage400, 400, summaryMissingMessage, reportDate);

        List<SLDSoat> sldSoats = new ArrayList<>();
        sldSoats.add(sldSoat100);
        sldSoats.add(sldSoat200);
        sldSoats.add(sldSoat101);
        sldSoats.add(sldSoat201);
        sldSoats.add(sldSoat203);
        sldSoats.add(sldSoat300);
        sldSoats.add(sldSoat400);
        data.addAll(sldSoats);
        DLieu901 dLieu901 = new DLieu901().DSSLDSoat(sldSoats);
        TTChung ttChung = new TTChung().PBan(pBan)
                .MNGui(tvanTaxCode)
                .MNNhan(tctTaxCode)
                .MLTDiep(MessageType._901_ThongDiepBaoCaoDoiSoatHangNgay.code())
                .MTDiep(mTDiep);
        TDiep901 diep901 = new TDiep901().TTChung(ttChung)
                .DLieu(dLieu901);
        return xmlMapper.writeValueAsString(diep901);
    }

    private void sendReportDocumentsToTCT(String reportDocuments, String messageId) {
        log.info("Send message 901 to TCT start with message id: {}", messageId);

        RequestInfo requestInfo = RequestInfo.builder()
                .createdAt(Instant.now())
                .initAt(Instant.now())
                .appId(tvanTaxCode)
                .taxCode(tvanTaxCode)
                .transactionId(messageId)
                .messageType(MessageType._901_ThongDiepBaoCaoDoiSoatHangNgay)
                .build();
        cacheService.saveRequestInfo(requestInfo);

        ListenableFuture<SendResult<String, String>> listenableFuture =
                tctKafkaTemplate.send(kafkaQueueIn, reportDocuments);
        listenableFuture.addCallback(new MdcListenableFutureCallback<>(
                result -> handleSend901Success(reportDocuments, messageId),
                ex -> handleSend901Failure(ex, messageId, reportDocuments)
        ));
        log.info("Send daily message report with id: {} to TCT successfully", messageId);
    }

    private SLDSoat creatSLDSoat(List<DailyMessageReportTct> listDailyMessage, Integer messageType,
                                 List<MissingMessage> summaryMissingMessage, LocalDate reportDate)
            throws NullPointerException{
        DailyMessageReportTct sentMessage = listDailyMessage.stream()
                .filter(d -> d.getResponseType() == null).findAny().orElse(null);
        DailyMessageReportTct messageError = listDailyMessage.stream()
                .filter(d -> d.getResponseType() == -1).findAny().orElse(null);
        DailyMessageReportTct message999NoError = listDailyMessage.stream().filter(d -> d.getResponseType() == 9991)
                .findAny().orElse(null);
        DailyMessageReportTct message999Error = listDailyMessage.stream().filter(d -> d.getResponseType() == 9990)
                .findAny().orElse(null);

        List<DSPHoi> dspHoiList = new ArrayList<>();
        int totalReceived = messageError.getTotal() + message999NoError.getTotal() + message999Error.getTotal();
        int totalLech = Math.abs(sentMessage.getTotal() - totalReceived);
        DSPHoi dspHoi9991 = new DSPHoi()
                .MLPHoi(ReconciliationMessageType.RECEIVED_RESPONSE.code())
                .SLGNhan(totalReceived)
                .SLGLech(totalLech);

        if (totalLech > 0) {
            List<String> dstdLech9991 = getListMissingMessageId(messageType, 999,
                    summaryMissingMessage, reportDate);
            dspHoi9991.setDSTDLech(dstdLech9991);
        }

        List<SLNCTiet> slncTiets = new ArrayList<>();
        SLNCTiet slncTiet0 = new SLNCTiet()
                .TTPHoi(ReconciliationMessageType.NO_ERROR_ACKNOWLEDGED_STATUS.code())
                .SLuong(message999NoError.getTotal());
        SLNCTiet slncTiet1 = new SLNCTiet()
                .TTPHoi(ReconciliationMessageType.ERROR_ACKNOWLEDGED_STATUS.code())
                .SLuong(message999Error.getTotal());
        slncTiets.add(slncTiet0);
        slncTiets.add(slncTiet1);

        int total999 = message999NoError.getTotal() + message999Error.getTotal();
        DSPHoi dspHoi999 = new DSPHoi()
                .MLPHoi(ReconciliationMessageType.ACKNOWLEDGED.code())
                .SLGNhan(total999)
                .SLGLech(0)
                .DSSLNCTiet(slncTiets);

        DSPHoi dspHoi1 = new DSPHoi()
                .MLPHoi(ReconciliationMessageType.FAIL.code())
                .SLGNhan(messageError.getTotal())
                .SLGLech(0);
        dspHoiList.add(dspHoi9991);
        dspHoiList.add(dspHoi999);
        dspHoiList.add(dspHoi1);

        if (messageType == 100 || messageType == 101) {
            DailyMessageReportTct message1022 = listDailyMessage.stream().filter(d -> d.getResponseType() == 1022)
                    .findAny().orElse(null);
            DailyMessageReportTct message1023 = listDailyMessage.stream().filter(d -> d.getResponseType() == 1023)
                    .findAny().orElse(null);
            DailyMessageReportTct message1034 = listDailyMessage.stream().filter(d -> d.getResponseType() == 1034)
                    .findAny().orElse(null);
            DailyMessageReportTct message1035 = listDailyMessage.stream().filter(d -> d.getResponseType() == 1035)
                    .findAny().orElse(null);

            List<SLNCTiet> slncTiet102 = new ArrayList<>();
            SLNCTiet slncTiet2 = new SLNCTiet()
                    .TTPHoi(ReconciliationMessageType.ERROR_RECEIVED_STATUS.code())
                    .SLuong(message1022.getTotal());
            SLNCTiet slncTiet3 = new SLNCTiet()
                    .TTPHoi(ReconciliationMessageType.NO_ERROR_RECEIVED_STATUS.code())
                    .SLuong(message1023.getTotal());

            slncTiet102.add(slncTiet2);
            slncTiet102.add(slncTiet3);
            int totalLech102 = Math.abs(message999NoError.getTotal() - (message1022.getTotal() + message1023.getTotal()));
            DSPHoi dspHoi102 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.RECEIVED.code())
                    .SLGNhan(message1022.getTotal() + message1023.getTotal())
                    .SLGLech(totalLech102)
                    .DSSLNCTiet(slncTiet102);

            if(totalLech102 > 0) {
                List<String> dstdLech = getListMissingMessageId(messageType, 102, summaryMissingMessage, reportDate);
                dspHoi102.setDSTDLech(dstdLech);
            }
            DSPHoi dspHoi103 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.CONFIRM.code())
                    .SLGNhan(message1034.getTotal() + message1035.getTotal())
                    .SLGLech(Math.abs(message1023.getTotal() - (message1034.getTotal() + message1035.getTotal())))
                    .DSTDLech(new ArrayList<>());

            dspHoiList.add(dspHoi102);
            dspHoiList.add(dspHoi103);

        }

        if (messageType == 200 || messageType == 201) {
            DailyMessageReportTct message202 = listDailyMessage.stream().filter(d -> d.getResponseType() == 202)
                    .findAny().orElse(null);
            DailyMessageReportTct message204 = listDailyMessage.stream().filter(d -> d.getResponseType() == 204)
                    .findAny().orElse(null);

            int totalReceived202204 = message202.getTotal() + message204.getTotal();
            int totalLech202204 = Math.abs(total999 - totalReceived202204);
            DSPHoi dspHoi202204 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.RESPONSE_FROM_TCT.code())
                    .SLGNhan(totalReceived202204)
                    .SLGLech(totalLech202204);

            if (totalLech202204 > 0) {
                List<String> dstdLech = getListMissingMessageId(messageType, 202, summaryMissingMessage, reportDate);
                dspHoi202204.setDSTDLech(dstdLech);
            }

            DSPHoi dspHoi202 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.RESPONSE_FROM_TCT_SUCCESS.code())
                    .SLGNhan(message202.getTotal())
                    .SLGLech(0);

            DSPHoi dspHoi204 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.RESPONSE_FROM_TCT_ERROR.code())
                    .SLGNhan(message204.getTotal())
                    .SLGLech(0);
            dspHoiList.add(dspHoi202204);
            dspHoiList.add(dspHoi202);
            dspHoiList.add(dspHoi204);
        }

        if (messageType == 203 || messageType == 400) {
            DailyMessageReportTct message204 = listDailyMessage.stream().filter(d -> d.getResponseType() == 204)
                    .findAny().orElse(null);
            int totalLech204 = Math.abs(message999NoError.getTotal() - message204.getTotal());
            DSPHoi dspHoi204 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.RESPONSE_FROM_TCT_FOR_REPORT.code())
                    .SLGNhan(message204.getTotal())
                    .SLGLech(totalLech204)
                    .DSSLNCTiet(new ArrayList<>());
            if (totalLech204 > 0) {
                List<String> dstdLech204 = getListMissingMessageId(messageType, 204, summaryMissingMessage, reportDate);
                dspHoi204.setDSTDLech(dstdLech204);
            }
            dspHoiList.add(dspHoi204);
        }

        if (messageType == 300) {
            DailyMessageReportTct message301 = listDailyMessage.stream().filter(d -> d.getResponseType() == 301)
                    .findAny().orElse(null);
            DailyMessageReportTct message2042 = listDailyMessage.stream().filter(d -> d.getResponseType() == 2042)
                    .findAny().orElse(null);
            DailyMessageReportTct message2043 = listDailyMessage.stream().filter(d -> d.getResponseType() == 2043)
                    .findAny().orElse(null);


            List<SLNCTiet> slncTiet204 = new ArrayList<>();
            SLNCTiet slncTiet2 = new SLNCTiet()
                    .TTPHoi(ReconciliationMessageType.ERROR_RECEIVED_STATUS.code())
                    .SLuong(message2042.getTotal());

            slncTiet204.add(slncTiet2);

            DSPHoi dspHoi204 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.RESPONSE_FROM_TCT_FOR_REPORT.code())
                    .SLGNhan(message2042.getTotal() + message2043.getTotal())
                    .SLGLech(0)
                    .DSSLNCTiet(slncTiet204);

            DSPHoi dspHoi301 = new DSPHoi()
                    .MLPHoi(ReconciliationMessageType.RESPONSE_CONFIRM_AFTER_204.code())
                    .SLGNhan(message301.getTotal())
                    .SLGLech(Math.abs(message999NoError.getTotal() - message2042.getTotal() - message301.getTotal()))
                    .DSTDLech(new ArrayList<>());
            dspHoiList.add(dspHoi204);
            dspHoiList.add(dspHoi301);
        }

        return new SLDSoat().MLTDiep(messageType.toString())
                .Ngay(DATE_ONLY_FORMATTER.format(reportDate))
                .SLDGui(sentMessage.getTotal())
                .DSPHLech(dspHoiList);
    }

    private List<String> getListMissingMessageId(Integer messageType, Integer expectedType,
                                                 List<MissingMessage> summaryMissingMessage, LocalDate reportDate) {
        Instant yesterday = Instant.from(reportDate.atStartOfDay(ZoneOffset.UTC));
        Instant today = yesterday.plus(1, ChronoUnit.DAYS);
        List<MissingMessage> missingMessages = missingMessageRepository.findMissingMessages(messageType, expectedType,
                yesterday, today);
        if (missingMessages.isEmpty()) {
            return Collections.emptyList();
        }
        summaryMissingMessage.addAll(missingMessages);
        return missingMessages.stream().map(MissingMessage::getId).collect(Collectors.toList());
    }

    private void handleSend901Success(String xml, String messageId) {
        log.info("Send message 901 to tct success");
        eventService.push(Event.builder()
                .createdAt(Instant.now())
                .initAt(Instant.now())
                .transactionId(messageId)
                .appId(tvanTaxCode)
                .taxCode(tvanTaxCode)
                .messageType(MessageType._901_ThongDiepBaoCaoDoiSoatHangNgay)
                .messageId(messageId)
                .type(EventType.SEND_901_MESSAGE_TO_TCT_SUCCESS)
                .isAuthorized(false)
                .data(xml)
                .build());
    }

    private void handleSend901Failure(Throwable ex, String messageId, String xml) {
        log.error("Send message 901 to tct fail", ex);
        eventService.push(Event.builder()
                .createdAt(Instant.now())
                .initAt(Instant.now())
                .transactionId(messageId)
                .appId(tvanTaxCode)
                .taxCode(tvanTaxCode)
                .messageType(MessageType._901_ThongDiepBaoCaoDoiSoatHangNgay)
                .messageId(messageId)
                .type(EventType.SEND_901_MESSAGE_TO_TCT_FAILED)
                .isAuthorized(false)
                .data(xml)
                .build());

    }
}
