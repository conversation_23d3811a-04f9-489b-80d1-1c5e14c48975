package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.ErrorWarningService;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.send-email-error-warning", havingValue = "true")
@Slf4j
public class ErrorWarningJob {
    private final ErrorWarningService errorWarningService;

    public ErrorWarningJob(ErrorWarningService errorWarningService) {
        this.errorWarningService = errorWarningService;
    }


    @Scheduled(cron = "${scheduler.job.cron.send-email-error-warning}")
    public void checkErrorToWarning() {
        long startTime = System.currentTimeMillis();
        MDC.put(TRACE_ID, String.valueOf(startTime));

        errorWarningService.errorWarning();

        log.info("Warning the errors execute time = {}ms", System.currentTimeMillis() - startTime);
        MDC.clear();
    }
}
