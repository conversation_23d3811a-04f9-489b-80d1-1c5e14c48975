package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.ContractService;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.update-contract-status", havingValue = "true")
@Slf4j
public class UpdateContractStatusJob {
    private final ContractService contractService;

    public UpdateContractStatusJob(ContractService contractService) {
        this.contractService = contractService;
    }

    @Scheduled(cron = "${scheduler.job.cron.update-contract-status}")
    public void updateContractStatus() {
        long startTime = System.currentTimeMillis();
        MDC.put(TRACE_ID, String.valueOf(startTime));

        contractService.updateContractStatus();

        log.info("Update contract status execute time = {}ms", System.currentTimeMillis() - startTime);
        MDC.clear();
    }
}
