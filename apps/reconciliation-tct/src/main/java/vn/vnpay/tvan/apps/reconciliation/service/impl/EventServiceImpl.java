package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import vn.vnpay.tvan.apps.reconciliation.service.EventService;
import vn.vnpay.tvan.libs.common.model.Event;
import vn.vnpay.tvan.libs.common.util.MdcListenableFutureCallback;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EventServiceImpl implements EventService {

    @Value("${spring.kafka.topics.event-topics}")
    private String eventTopics;

    private final KafkaTemplate<String, Object> kafkaTemplate;

    public EventServiceImpl(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public void push(Event<?> event) {
        pushToQueue(eventTopics, event.getTransactionId(), event);
    }

    public void pushToQueue(String topic, String key, Object data) {
        if (log.isTraceEnabled()) {
            log.trace("Push to topic: {}, data: {}", topic, data);
        } else {
            log.info("Push to topic: {}", topic);
        }
        ListenableFuture<SendResult<String, Object>> listenableFuture = kafkaTemplate.send(topic, key, data);

        listenableFuture.addCallback(new MdcListenableFutureCallback<>(
                sendResult -> log.info("Sent message to topic {} success", topic),
                ex -> log.error("Sent message to topic " + topic + " fail:", ex)
        ));
    }

}
