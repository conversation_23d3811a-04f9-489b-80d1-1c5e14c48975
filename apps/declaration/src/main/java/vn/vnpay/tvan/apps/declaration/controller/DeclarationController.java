package vn.vnpay.tvan.apps.declaration.controller;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpay.tvan.apps.declaration.model.TaxCodeInfoModel;
import vn.vnpay.tvan.apps.declaration.model.request.TaxCodeInformationRequest;
import vn.vnpay.tvan.apps.declaration.service.TaxCodeService;
import vn.vnpay.tvan.libs.common.model.Response;

import java.util.Collections;
import java.util.List;

@RestController
@Data
public class DeclarationController {
    private final TaxCodeService taxCodeService;
    @Value("${declaration.api.max-element:10}")
    private int maxElement;
    public DeclarationController(TaxCodeService taxCodeService) {
        this.taxCodeService = taxCodeService;
    }

    @PostMapping("/v1/taxcode/information")
    public ResponseEntity<List<TaxCodeInfoModel>> getTaxCodesInfo(@RequestBody TaxCodeInformationRequest request) throws Exception {
        List<String> taxCodes = request.getTaxCodes();
        if (CollectionUtils.isEmpty(taxCodes)) {
            return ResponseEntity.ok(Collections.emptyList());
        }

        //Only get result for first n Element
        taxCodes = taxCodes.stream().limit(maxElement).toList();
        return ResponseEntity.ok(taxCodeService.getInfoByTaxCodes(taxCodes));
    }

    @PostMapping("/v2/taxcode/information")
    public ResponseEntity<Response<List<TaxCodeInfoModel>>> getTaxCodesInfoV2(@RequestBody TaxCodeInformationRequest request) throws Exception {
        List<String> taxCodes = request.getTaxCodes();
        if (CollectionUtils.isEmpty(taxCodes)) {
            return ResponseEntity.ok(Response.ok(Collections.emptyList()));
        }

        //Only get result for first n Element
        taxCodes = taxCodes.stream().limit(maxElement).toList();
        List<TaxCodeInfoModel> taxCodeInfoList = taxCodeService.getInfoByTaxCodes(taxCodes);

        return ResponseEntity.ok(Response.ok(taxCodeInfoList));
    }
}
