package vn.vnpay.tvan.apps.digitalsignature.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.w3c.dom.Attr;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import vn.vnpay.tvan.apps.digitalsignature.exception.BadRequestException;
import vn.vnpay.tvan.apps.digitalsignature.services.SignXmlService;
import vn.vnpay.tvan.apps.digitalsignature.utils.XmlUtil;

import javax.xml.crypto.dom.DOMStructure;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.DigestMethod;
import javax.xml.crypto.dsig.Reference;
import javax.xml.crypto.dsig.SignatureMethod;
import javax.xml.crypto.dsig.SignatureProperties;
import javax.xml.crypto.dsig.SignatureProperty;
import javax.xml.crypto.dsig.SignedInfo;
import javax.xml.crypto.dsig.Transform;
import javax.xml.crypto.dsig.XMLSignature;
import javax.xml.crypto.dsig.XMLSignatureFactory;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SignService implements SignXmlService {

    @Value("${mst.999}")
    private String mst999;

    @Value("${mst.998}")
    private String mst998;

    @Value("${jks.password}")
    private String password;

    @Value("${jks.alias}")
    private String alias;

    @Value("${folder.path}")
    private String path;


    @Override
    public String signFile(InputStream source) throws Exception {
        List<String> validTaxCodes = List.of(mst998, mst999);

        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document doc = dbf.newDocumentBuilder().parse(source);

        DOMSource domSource = new DOMSource(doc);
        StringWriter writer = new StringWriter();
        StreamResult result = new StreamResult(writer);
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        transformer.transform(domSource, result);

        String sourceContent = writer.toString();
        String modifyContent = sourceContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String nBan = XmlUtil.getOuterXmlUsingSubString("NBan", modifyContent);
        assert nBan != null;
        String mstNnt = XmlUtil.getInnerXmlUsingSubString("MST", nBan);

        if (!validTaxCodes.contains(mstNnt)) {
            throw new BadRequestException("Tax code must be one of the following values: " + mst998 + " or " + mst999);
        }

        ClassLoader classloader = Thread.currentThread().getContextClassLoader();
        String type = mstNnt.equalsIgnoreCase(mst999) ? "999" : "998";
        InputStream jks = classloader.getResourceAsStream(type + ".jks");

        return signHdon(modifyContent, jks, "xml");
    }

    @Override
    public String process200(InputStream source) throws Exception {
        List<String> validTaxCodes = List.of(mst998, mst999);

        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document doc = dbf.newDocumentBuilder().parse(source);

        DOMSource domSource = new DOMSource(doc);
        StringWriter writer = new StringWriter();
        StreamResult result = new StreamResult(writer);
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        transformer.transform(domSource, result);

        String sourceContent = writer.toString();
        String modifyContent = sourceContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String nBan = XmlUtil.getOuterXmlUsingSubString("NBan", modifyContent);
        assert nBan != null;
        String mstNnt = XmlUtil.getInnerXmlUsingSubString("MST", nBan);

        if (!validTaxCodes.contains(mstNnt)) {
            throw new BadRequestException("Tax code must be one of the following values: " + mst998 + " or " + mst999);
        }

        ClassLoader classloader = Thread.currentThread().getContextClassLoader();
        String type = mstNnt.equalsIgnoreCase(mst999) ? "999" : "998";
        InputStream jks = classloader.getResourceAsStream(type + ".jks");

        return signHdon(modifyContent, jks, "json");
    }

    @Override
    public String sign300(InputStream source) throws Exception {
        List<String> validTaxCodes = List.of(mst998, mst999);

        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document doc = dbf.newDocumentBuilder().parse(source);

        DOMSource domSource = new DOMSource(doc);
        StringWriter writer = new StringWriter();
        StreamResult result = new StreamResult(writer);
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        transformer.transform(domSource, result);

        String sourceContent = writer.toString();
        String modifyContent = sourceContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String dltBao = XmlUtil.getOuterXmlUsingSubString("DLTBao", modifyContent);
        assert dltBao != null;
        String mstNnt = XmlUtil.getInnerXmlUsingSubString("MST", dltBao);

        if (!validTaxCodes.contains(mstNnt)) {
            throw new BadRequestException("Tax code must be one of the following values: " + mst998 + " or " + mst999);
        }

        ClassLoader classloader = Thread.currentThread().getContextClassLoader();
        String type = mstNnt.equalsIgnoreCase(mst999) ? "999" : "998";
        InputStream jks = classloader.getResourceAsStream(type + ".jks");

        return signTbao(modifyContent, jks, password, alias, "xml");
    }

    public String sign400(InputStream source) throws Exception {
        List<String> validTaxCodes = List.of(mst998, mst999);

        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document doc = dbf.newDocumentBuilder().parse(source);

        DOMSource domSource = new DOMSource(doc);
        StringWriter writer = new StringWriter();
        StreamResult result = new StreamResult(writer);
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        transformer.transform(domSource, result);

        String sourceContent = writer.toString();
        String modifyContent = sourceContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String dltBao = XmlUtil.getOuterXmlUsingSubString("TTChung", modifyContent);
        assert dltBao != null;
        String mstNnt = XmlUtil.getInnerXmlUsingSubString("MST", dltBao);

        if (!validTaxCodes.contains(mstNnt)) {
            throw new BadRequestException("Tax code must be one of the following values: " + mst998 + " or " + mst999);
        }

        ClassLoader classloader = Thread.currentThread().getContextClassLoader();
        String type = mstNnt.equalsIgnoreCase(mst999) ? "999" : "998";
        InputStream jks = classloader.getResourceAsStream(type + ".jks");

        return signBangTongHop(modifyContent, jks, password, alias, "json");
    }

    public String sign100(InputStream source) throws Exception {
        List<String> validTaxCodes = List.of(mst998, mst999);

        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document doc = dbf.newDocumentBuilder().parse(source);

        DOMSource domSource = new DOMSource(doc);
        StringWriter writer = new StringWriter();
        StreamResult result = new StreamResult(writer);
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        transformer.transform(domSource, result);

        String sourceContent = writer.toString();
        String modifyContent = sourceContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String ttChung = XmlUtil.getOuterXmlUsingSubString("TTChung", modifyContent);
        assert ttChung != null;
        String mstNnt = XmlUtil.getInnerXmlUsingSubString("MST", ttChung);

        if (!validTaxCodes.contains(mstNnt)) {
            throw new BadRequestException("Tax code must be one of the following values: " + mst998 + " or " + mst999);
        }

        ClassLoader classloader = Thread.currentThread().getContextClassLoader();
        String type = mstNnt.equalsIgnoreCase(mst999) ? "999" : "998";
        InputStream jks = classloader.getResourceAsStream(type + ".jks");

        return signToKhai(modifyContent, jks, password, alias, "json");
    }

    public String signHdon(String modifyContent, InputStream jks, String returnType) throws Exception {
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        String modifyXmlFileName = path + System.currentTimeMillis() + "modify.xml";

        FileOutputStream outputStream = new FileOutputStream(modifyXmlFileName);
        IOUtils.write(modifyContent.getBytes(StandardCharsets.UTF_8), outputStream);
        outputStream.close();
        Document doc;
        try (FileInputStream modifyXml = new FileInputStream(modifyXmlFileName)) {

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            doc = dbf.newDocumentBuilder().parse(modifyXml);
        }

        createDKCKSforHDon(doc);

        Element dLHDon = (Element) doc.getElementsByTagName("DLHDon").item(0);
        Attr idAttr = dLHDon.getAttributeNode("Id");
        String invoiceId = idAttr.getValue();
        dLHDon.setIdAttributeNode(idAttr, true);

        Element nBanElement = (Element) doc.getElementsByTagName("NBan").item(0);
        Element mstElement = (Element) nBanElement.getElementsByTagName("MST").item(0);
        String mstNnt = mstElement.getTextContent();

        Reference ref1 = fac.newReference
                ("#" + invoiceId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null)
                                , fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null)
                        )
                        , null, null);

        String signTimeId = "SigningTime-" + System.currentTimeMillis();

        Reference ref2 = fac.newReference
                ("#" + signTimeId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null),
                                fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null))
                        , null, null);

        // Create the SignedInfo.
        SignedInfo signedInfo = fac.newSignedInfo
                (fac.newCanonicalizationMethod
                                (CanonicalizationMethod.INCLUSIVE,
                                        (C14NMethodParameterSpec) null),
                        fac.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
                        List.of(ref1, ref2));

        // Load the KeyStore and get the signing key and certificate.
        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(jks, password.toCharArray());

        KeyStore.PrivateKeyEntry keyEntry =
                (KeyStore.PrivateKeyEntry) ks.getEntry
                        (alias, new KeyStore.PasswordProtection(password.toCharArray()));
        X509Certificate cert = (X509Certificate) keyEntry.getCertificate();

        // Create the KeyInfo containing the X509Data.
        KeyInfoFactory kif = fac.getKeyInfoFactory();
        List<Object> x509Content = new ArrayList<>();
        x509Content.add(cert.getSubjectX500Principal().getName());
        x509Content.add(cert);
        X509Data xd = kif.newX509Data(x509Content);
        KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(xd));

        Element signingTime = doc.createElement("SigningTime");
        signingTime.setTextContent(getDateString());
        SignatureProperty sp = fac.newSignatureProperty(Collections.singletonList(new DOMStructure(signingTime)), "signatureProperties", null);
        SignatureProperties sps = fac.newSignatureProperties(Collections.singletonList(sp), null);

        Element dscks = (Element) doc.getElementsByTagName("DSCKS").item(0);
        Element nBan = (Element) dscks.getElementsByTagName("NBan").item(0);

        // Create a DOMSignContext and specify the RSA PrivateKey and
        // location of the resulting XMLSignature's parent element.
        DOMSignContext dsc = new DOMSignContext
                (keyEntry.getPrivateKey(), nBan);

        // Create the XMLSignature, but don't sign it yet.
        XMLSignature signature = fac.newXMLSignature(
                signedInfo,
                keyInfo,
                Collections.singletonList(
                        fac.newXMLObject(Collections.singletonList(sps), signTimeId, null, null)
                ),
                null
                ,
                null);
        // Marshal, generate, and sign the enveloped signature.
        signature.sign(dsc);

        Element sig = (Element) doc.getElementsByTagName("SignatureValue").item(0);
        String sigValue = sig.getTextContent().replaceAll("\r\n", "");
        sig.setTextContent(sigValue);

        Element x509Cert = (Element) doc.getElementsByTagName("X509Certificate").item(0);
        String certValue = x509Cert.getTextContent().replaceAll("\r\n", "");
        x509Cert.setTextContent(certValue);

        // Output the resulting document.
        OutputStream os = new FileOutputStream(path + "signedDocument" + invoiceId + ".xml");
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer trans = tf.newTransformer();
        trans.transform(new DOMSource(doc), new StreamResult(os));

        String signFileName = path + "signedDocument" + invoiceId + ".xml";

        String fileContent =
                IOUtils.toString(new FileInputStream(signFileName), StandardCharsets.UTF_8);
        String resultContent = fileContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String base64Content = Base64.getEncoder().encodeToString(resultContent.getBytes(StandardCharsets.UTF_8));
        Map<String, String> body = new HashMap<>();
        body.put("mstNnt", mstNnt);
        body.put("data", base64Content);
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(body);

        IOUtils.close();
        os.close();

        File signFile = new File(signFileName);
        File modifyFile = new File(modifyXmlFileName);
        signFile.delete();
        modifyFile.delete();

        if (returnType.equals("xml")) {
            return  resultContent;
        } else {
            return json;
        }
    }

    public String signTbao(String modifyContent, InputStream jks, String password, String alias, String returnType) throws Exception {
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        String modifyXmlFileName = path + System.currentTimeMillis() + "modify.xml";

        FileOutputStream outputStream = new FileOutputStream(modifyXmlFileName);
        IOUtils.write(modifyContent.getBytes(StandardCharsets.UTF_8), outputStream);
        outputStream.close();
        Document doc;
        try (FileInputStream modifyXml = new FileInputStream(modifyXmlFileName)) {

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            doc = dbf.newDocumentBuilder().parse(modifyXml);
        }

        createDKCKSforTBao(doc);

        Element dltBao = (Element) doc.getElementsByTagName("DLTBao").item(0);
        Attr idAttr = dltBao.getAttributeNode("Id");
        String invoiceId = idAttr.getValue();
        dltBao.setIdAttributeNode(idAttr, true);

        Element mstElement = (Element) dltBao.getElementsByTagName("MST").item(0);
        String mstNnt = mstElement.getTextContent();

        Reference ref1 = fac.newReference
                ("#" + invoiceId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null)
                                , fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null)
                        )
                        , null, null);

        String signTimeId = "SigningTime-" + System.currentTimeMillis();

        Reference ref2 = fac.newReference
                ("#" + signTimeId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null),
                                fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null))
                        , null, null);

        // Create the SignedInfo.
        SignedInfo signedInfo = fac.newSignedInfo
                (fac.newCanonicalizationMethod
                                (CanonicalizationMethod.INCLUSIVE,
                                        (C14NMethodParameterSpec) null),
                        fac.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
                        List.of(ref1, ref2));

        // Load the KeyStore and get the signing key and certificate.
        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(jks, password.toCharArray());

        KeyStore.PrivateKeyEntry keyEntry =
                (KeyStore.PrivateKeyEntry) ks.getEntry
                        (alias, new KeyStore.PasswordProtection(password.toCharArray()));
        X509Certificate cert = (X509Certificate) keyEntry.getCertificate();

        // Create the KeyInfo containing the X509Data.
        KeyInfoFactory kif = fac.getKeyInfoFactory();
        List<Object> x509Content = new ArrayList<>();
        x509Content.add(cert.getSubjectX500Principal().getName());
        x509Content.add(cert);
        X509Data xd = kif.newX509Data(x509Content);
        KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(xd));

        Element signingTime = doc.createElement("SigningTime");
        signingTime.setTextContent(getDateString());
        SignatureProperty sp = fac.newSignatureProperty(Collections.singletonList(new DOMStructure(signingTime)), "signatureProperties", null);
        SignatureProperties sps = fac.newSignatureProperties(Collections.singletonList(sp), null);

        Element dscks = (Element) doc.getElementsByTagName("DSCKS").item(0);
        Element nnt = (Element) dscks.getElementsByTagName("NNT").item(0);

        // Create a DOMSignContext and specify the RSA PrivateKey and
        // location of the resulting XMLSignature's parent element.
        DOMSignContext dsc = new DOMSignContext
                (keyEntry.getPrivateKey(), nnt);

        // Create the XMLSignature, but don't sign it yet.
        XMLSignature signature = fac.newXMLSignature(
                signedInfo,
                keyInfo,
                Collections.singletonList(
                        fac.newXMLObject(Collections.singletonList(sps), signTimeId, null, null)
                ),
                null
                ,
                null);
        // Marshal, generate, and sign the enveloped signature.
        signature.sign(dsc);

        Element sig = (Element) doc.getElementsByTagName("SignatureValue").item(0);
        String sigValue = sig.getTextContent().replaceAll("\r\n", "");
        sig.setTextContent(sigValue);

        Element x509Cert = (Element) doc.getElementsByTagName("X509Certificate").item(0);
        String certValue = x509Cert.getTextContent().replaceAll("\r\n", "");
        x509Cert.setTextContent(certValue);

        // Output the resulting document.
        OutputStream os = new FileOutputStream(path + "signedDocument" + invoiceId + ".xml");
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer trans = tf.newTransformer();
        trans.transform(new DOMSource(doc), new StreamResult(os));

        String signFileName = path + "signedDocument" + invoiceId + ".xml";

        String fileContent =
                IOUtils.toString(new FileInputStream(signFileName), StandardCharsets.UTF_8);
        String resultContent = fileContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String base64Content = Base64.getEncoder().encodeToString(resultContent.getBytes(StandardCharsets.UTF_8));
        Map<String, String> body = new HashMap<>();
        body.put("mstNnt", mstNnt);
        body.put("data", base64Content);
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(body);

        IOUtils.close();
        os.close();

        File signFile = new File(signFileName);
        File modifyFile = new File(modifyXmlFileName);
        signFile.delete();
        modifyFile.delete();

        if (returnType.equals("xml")) {
            return resultContent;
        } else {
            return json;
        }
    }

    public String signBangTongHop(String modifyContent, InputStream jks, String password, String alias, String returnType) throws Exception {
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        String modifyXmlFileName = path + System.currentTimeMillis() + "modify.xml";

        FileOutputStream outputStream = new FileOutputStream(modifyXmlFileName);
        IOUtils.write(modifyContent.getBytes(StandardCharsets.UTF_8), outputStream);
        outputStream.close();
        Document doc;
        try (FileInputStream modifyXml = new FileInputStream(modifyXmlFileName)) {

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            doc = dbf.newDocumentBuilder().parse(modifyXml);
        }

        createDKCKSforBangTongHop(doc);

        Element dlbtHop = (Element) doc.getElementsByTagName("DLBTHop").item(0);
        Attr idAttr = dlbtHop.getAttributeNode("Id");
        String invoiceId = idAttr.getValue();
        dlbtHop.setIdAttributeNode(idAttr, true);

        Element ttChung = (Element) dlbtHop.getElementsByTagName("TTChung").item(0);
        Element mstElement = (Element) ttChung.getElementsByTagName("MST").item(0);
        String mstNnt = mstElement.getTextContent();

        Reference ref1 = fac.newReference
                ("#" + invoiceId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null)
                                , fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null)
                        )
                        , null, null);

        String signTimeId = "SigningTime-" + System.currentTimeMillis();

        Reference ref2 = fac.newReference
                ("#" + signTimeId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null),
                                fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null))
                        , null, null);

        // Create the SignedInfo.
        SignedInfo signedInfo = fac.newSignedInfo
                (fac.newCanonicalizationMethod
                                (CanonicalizationMethod.INCLUSIVE,
                                        (C14NMethodParameterSpec) null),
                        fac.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
                        List.of(ref1, ref2));

        // Load the KeyStore and get the signing key and certificate.
        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(jks, password.toCharArray());

        KeyStore.PrivateKeyEntry keyEntry =
                (KeyStore.PrivateKeyEntry) ks.getEntry
                        (alias, new KeyStore.PasswordProtection(password.toCharArray()));
        X509Certificate cert = (X509Certificate) keyEntry.getCertificate();

        // Create the KeyInfo containing the X509Data.
        KeyInfoFactory kif = fac.getKeyInfoFactory();
        List<Object> x509Content = new ArrayList<>();
        x509Content.add(cert.getSubjectX500Principal().getName());
        x509Content.add(cert);
        X509Data xd = kif.newX509Data(x509Content);
        KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(xd));

        Element signingTime = doc.createElement("SigningTime");
        signingTime.setTextContent(getDateString());
        SignatureProperty sp = fac.newSignatureProperty(Collections.singletonList(new DOMStructure(signingTime)), "signatureProperties", null);
        SignatureProperties sps = fac.newSignatureProperties(Collections.singletonList(sp), null);

        Element dscks = (Element) doc.getElementsByTagName("DSCKS").item(0);
        Element nnt = (Element) dscks.getElementsByTagName("NNT").item(0);

        // Create a DOMSignContext and specify the RSA PrivateKey and
        // location of the resulting XMLSignature's parent element.
        DOMSignContext dsc = new DOMSignContext
                (keyEntry.getPrivateKey(), nnt);

        // Create the XMLSignature, but don't sign it yet.
        XMLSignature signature = fac.newXMLSignature(
                signedInfo,
                keyInfo,
                Collections.singletonList(
                        fac.newXMLObject(Collections.singletonList(sps), signTimeId, null, null)
                ),
                null
                ,
                null);
        // Marshal, generate, and sign the enveloped signature.
        signature.sign(dsc);

        Element sig = (Element) doc.getElementsByTagName("SignatureValue").item(0);
        String sigValue = sig.getTextContent().replaceAll("\r\n", "");
        sig.setTextContent(sigValue);

        Element x509Cert = (Element) doc.getElementsByTagName("X509Certificate").item(0);
        String certValue = x509Cert.getTextContent().replaceAll("\r\n", "");
        x509Cert.setTextContent(certValue);

        // Output the resulting document.
        OutputStream os = new FileOutputStream(path + "signedDocument" + invoiceId + ".xml");
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer trans = tf.newTransformer();
        trans.transform(new DOMSource(doc), new StreamResult(os));

        String signFileName = path + "signedDocument" + invoiceId + ".xml";

        String fileContent =
                IOUtils.toString(new FileInputStream(signFileName), StandardCharsets.UTF_8);
        String resultContent = fileContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String base64Content = Base64.getEncoder().encodeToString(resultContent.getBytes(StandardCharsets.UTF_8));
        Map<String, String> body = new HashMap<>();
        body.put("mstNnt", mstNnt);
        body.put("data", base64Content);
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(body);

        IOUtils.close();
        os.close();

        File signFile = new File(signFileName);
        File modifyFile = new File(modifyXmlFileName);
        signFile.delete();
        modifyFile.delete();

        if (returnType.equals("xml")) {
            return resultContent;
        } else {
            return json;
        }
    }

    public String signToKhai(String modifyContent, InputStream jks, String password, String alias, String returnType) throws Exception {
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        String modifyXmlFileName = path + System.currentTimeMillis() + "modify.xml";

        FileOutputStream outputStream = new FileOutputStream(modifyXmlFileName);
        IOUtils.write(modifyContent.getBytes(StandardCharsets.UTF_8), outputStream);
        outputStream.close();
        Document doc;
        try (FileInputStream modifyXml = new FileInputStream(modifyXmlFileName)) {

            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            doc = dbf.newDocumentBuilder().parse(modifyXml);
        }

        createDKCKSforToKhai(doc);

        Element dltKhai = (Element) doc.getElementsByTagName("DLTKhai").item(0);
        Attr idAttr = dltKhai.getAttributeNode("Id");
        String invoiceId = idAttr.getValue();
        dltKhai.setIdAttributeNode(idAttr, true);

        Element ttChung = (Element) dltKhai.getElementsByTagName("TTChung").item(0);
        Element mstElement = (Element) ttChung.getElementsByTagName("MST").item(0);
        String mstNnt = mstElement.getTextContent();

        Reference ref1 = fac.newReference
                ("#" + invoiceId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null)
                                , fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null)
                        )
                        , null, null);

        String signTimeId = "SigningTime-" + System.currentTimeMillis();

        Reference ref2 = fac.newReference
                ("#" + signTimeId, fac.newDigestMethod(DigestMethod.SHA256, null),
                        List.of(fac.newTransform("http://www.w3.org/2001/10/xml-exc-c14n#", (TransformParameterSpec) null),
                                fac.newTransform("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (TransformParameterSpec) null))
                        , null, null);

        // Create the SignedInfo.
        SignedInfo signedInfo = fac.newSignedInfo
                (fac.newCanonicalizationMethod
                                (CanonicalizationMethod.INCLUSIVE,
                                        (C14NMethodParameterSpec) null),
                        fac.newSignatureMethod(SignatureMethod.RSA_SHA256, null),
                        List.of(ref1, ref2));

        // Load the KeyStore and get the signing key and certificate.
        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(jks, password.toCharArray());

        KeyStore.PrivateKeyEntry keyEntry =
                (KeyStore.PrivateKeyEntry) ks.getEntry
                        (alias, new KeyStore.PasswordProtection(password.toCharArray()));
        X509Certificate cert = (X509Certificate) keyEntry.getCertificate();

        // Create the KeyInfo containing the X509Data.
        KeyInfoFactory kif = fac.getKeyInfoFactory();
        List<Object> x509Content = new ArrayList<>();
        x509Content.add(cert.getSubjectX500Principal().getName());
        x509Content.add(cert);
        X509Data xd = kif.newX509Data(x509Content);
        KeyInfo keyInfo = kif.newKeyInfo(Collections.singletonList(xd));

        Element signingTime = doc.createElement("SigningTime");
        signingTime.setTextContent(getDateString());
        SignatureProperty sp = fac.newSignatureProperty(Collections.singletonList(new DOMStructure(signingTime)), "signatureProperties", null);
        SignatureProperties sps = fac.newSignatureProperties(Collections.singletonList(sp), null);

        Element dscks = (Element) doc.getElementsByTagName("DSCKS").item(0);
        Element nnt = (Element) dscks.getElementsByTagName("NNT").item(0);

        // Create a DOMSignContext and specify the RSA PrivateKey and
        // location of the resulting XMLSignature's parent element.
        DOMSignContext dsc = new DOMSignContext
                (keyEntry.getPrivateKey(), nnt);

        // Create the XMLSignature, but don't sign it yet.
        XMLSignature signature = fac.newXMLSignature(
                signedInfo,
                keyInfo,
                Collections.singletonList(
                        fac.newXMLObject(Collections.singletonList(sps), signTimeId, null, null)
                ),
                null
                ,
                null);
        // Marshal, generate, and sign the enveloped signature.
        signature.sign(dsc);

        Element sig = (Element) doc.getElementsByTagName("SignatureValue").item(0);
        String sigValue = sig.getTextContent().replaceAll("\r\n", "");
        sig.setTextContent(sigValue);

        Element x509Cert = (Element) doc.getElementsByTagName("X509Certificate").item(0);
        String certValue = x509Cert.getTextContent().replaceAll("\r\n", "");
        x509Cert.setTextContent(certValue);

        // Output the resulting document.
        OutputStream os = new FileOutputStream(path + "signedDocument" + invoiceId + ".xml");
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer trans = tf.newTransformer();
        trans.transform(new DOMSource(doc), new StreamResult(os));

        String signFileName = path + "signedDocument" + invoiceId + ".xml";

        String fileContent =
                IOUtils.toString(new FileInputStream(signFileName), StandardCharsets.UTF_8);
        String resultContent = fileContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String base64Content = Base64.getEncoder().encodeToString(resultContent.getBytes(StandardCharsets.UTF_8));
        Map<String, String> body = new HashMap<>();
        body.put("mstNnt", mstNnt);
        body.put("data", base64Content);
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(body);

        IOUtils.close();
        os.close();

        File signFile = new File(signFileName);
        File modifyFile = new File(modifyXmlFileName);
        signFile.delete();
        modifyFile.delete();

        if (returnType.equals("xml")) {
            return resultContent;
        } else {
            return json;
        }
    }

    @Override
    public String createJsonRequestBody(InputStream source) throws IOException {
        String fileContent = IOUtils.toString(source, StandardCharsets.UTF_8);
        String resultContent = fileContent.replaceAll("\r\n", "")
                .replaceAll("\\s{2,}", "")
                .replaceAll("\t", "")
                .replaceAll("\\<\\?xml(.+?)\\?\\>", "").trim();

        String nBan = XmlUtil.getOuterXmlUsingSubString("NBan", resultContent);
        assert nBan != null;
        String mstNnt = XmlUtil.getInnerXmlUsingSubString("MST", nBan);

        String base64Content = Base64.getEncoder().encodeToString(resultContent.getBytes(StandardCharsets.UTF_8));
        Map<String, String> body = new HashMap<>();
        body.put("mstNnt", mstNnt);
        body.put("data", base64Content);
        ObjectMapper mapper = new ObjectMapper();

        return mapper.writeValueAsString(body);
    }

    public static String getDateString() {
        Date date = Calendar.getInstance().getTime();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss");

        return dateFormat.format(date);
    }

    public void createDKCKSforHDon(Document doc) {
        Element hDon = (Element) doc.getElementsByTagName("HDon").item(0);
        Element dscks = doc.createElement("DSCKS");
        Element nBan = doc.createElement("NBan");
        dscks.appendChild(nBan);
        hDon.appendChild(dscks);
    }

    public void createDKCKSforTBao(Document doc) {
        Element hDon = (Element) doc.getElementsByTagName("TBao").item(0);
        Element dscks = doc.createElement("DSCKS");
        Element nBan = doc.createElement("NNT");
        dscks.appendChild(nBan);
        hDon.appendChild(dscks);
    }

    public void createDKCKSforBangTongHop(Document doc) {
        Element hDon = (Element) doc.getElementsByTagName("BTHDLieu").item(0);
        Element dscks = doc.createElement("DSCKS");
        Element nBan = doc.createElement("NNT");
        dscks.appendChild(nBan);
        hDon.appendChild(dscks);
    }

    public void createDKCKSforToKhai(Document doc) {
        Element hDon = (Element) doc.getElementsByTagName("TKhai").item(0);
        Element dscks = doc.createElement("DSCKS");
        Element nBan = doc.createElement("NNT");
        dscks.appendChild(nBan);
        hDon.appendChild(dscks);
    }
}
