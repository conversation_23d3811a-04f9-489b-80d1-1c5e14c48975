package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.ExpiredCustomerService;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.check-customer-about-to-expired", havingValue = "true")
@Slf4j
public class AboutToExpiredCustomerJob {
    private final ExpiredCustomerService expiredCustomerService;

    public AboutToExpiredCustomerJob(ExpiredCustomerService expiredCustomerService) {
        this.expiredCustomerService = expiredCustomerService;
    }

    @Scheduled(cron = "${scheduler.job.cron.check-customer-about-to-expired}")
    public void checkCustomerABoutToExpire() {
        long startTime = System.currentTimeMillis();
        MDC.put(TRACE_ID, String.valueOf(startTime));

        expiredCustomerService.checkCustomersAboutToExpire();

        log.info("Checking about to execute time = {}ms", System.currentTimeMillis() - startTime);
        MDC.clear();
    }
}
