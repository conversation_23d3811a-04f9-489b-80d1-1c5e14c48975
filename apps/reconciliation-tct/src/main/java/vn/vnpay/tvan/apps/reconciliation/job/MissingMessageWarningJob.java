package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.MissingMessageWarningService;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.send-email-warning-tct-have-not-response-yet", havingValue = "true")
@Slf4j
public class MissingMessageWarningJob {
    private final MissingMessageWarningService missingMessageWarningService;

    public MissingMessageWarningJob(MissingMessageWarningService missingMessageWarningService) {
        this.missingMessageWarningService = missingMessageWarningService;
    }


    @Scheduled(cron = "${scheduler.job.cron.send-email-warning-tct-have-not-response-yet}")
    public void warningTCTHaveNotResponseYet() {
        long startTime = System.currentTimeMillis();
        MDC.put(TRACE_ID, String.valueOf(startTime));

        missingMessageWarningService.warningTransactionThatTctHaveNotResponseYet();

        log.info("Warning transactions that tct have not response yet execute time = {}ms", System.currentTimeMillis() - startTime);
        MDC.clear();
    }
}
