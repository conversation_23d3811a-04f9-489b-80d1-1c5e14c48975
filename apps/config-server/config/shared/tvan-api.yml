server:
  port: 8000

spring:
  kafka:
    admin:
      fail-fast: true
    bootstrap-servers: ${internal-kafka}
    create-topics: # config for topic creation
      partitions: 10 # number of partitions
      replicas: 1 # number of replicas
      min-insync-replicas: 1 # number of replicas that must be in sync with the leader before a write is considered successful and must be <= the number of replicas
    producer:
      bootstrap-servers: ${internal-kafka}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

app:
  api-key-ttl-in-seconds: 60
