package vn.vnpay.tvan.apps.reconciliation.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.util.concurrent.ListenableFuture;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.share.message.reconciliation.DLieu902;
import vn.vnpay.share.message.reconciliation.DSPHoi;
import vn.vnpay.share.message.reconciliation.SLDSoat;
import vn.vnpay.share.message.reconciliation.SLNCTiet;
import vn.vnpay.share.message.reconciliation.TDiep902;
import vn.vnpay.share.message.reconciliation.TTChung;
import vn.vnpay.tvan.apps.reconciliation.model.AuthorizeReconciliationResultModel;
import vn.vnpay.tvan.apps.reconciliation.model.EndAuthorizationProjection;
import vn.vnpay.tvan.apps.reconciliation.repository.AuthorizationSessionRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.DataReportHistoryRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.DifferentMessageReportRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.EndAuthorizationEventStoreRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.FailedMessageReportRepository;
import vn.vnpay.tvan.apps.reconciliation.service.AuthorizationSessionService;
import vn.vnpay.tvan.apps.reconciliation.service.CacheService;
import vn.vnpay.tvan.apps.reconciliation.service.EndAuthorizationResultService;
import vn.vnpay.tvan.apps.reconciliation.service.EventService;
import vn.vnpay.tvan.libs.common.constant.AuthorizeSessionStatus;
import vn.vnpay.tvan.libs.common.constant.Constant;
import vn.vnpay.tvan.libs.common.constant.EndAuthorizationEventStatus;
import vn.vnpay.tvan.libs.common.constant.EventType;
import vn.vnpay.tvan.libs.common.model.Event;
import vn.vnpay.tvan.libs.common.model.RequestInfo;
import vn.vnpay.tvan.libs.common.model.entity.AuthorizationSession;
import vn.vnpay.tvan.libs.common.model.entity.DataReportHistory;
import vn.vnpay.tvan.libs.common.model.entity.DifferentMessageReport;
import vn.vnpay.tvan.libs.common.model.entity.EndAuthorizationEventStore;
import vn.vnpay.tvan.libs.common.model.entity.FailedMessageReport;
import vn.vnpay.tvan.libs.common.util.MdcListenableFutureCallback;
import vn.vnpay.tvan.libs.common.util.Utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRANSACTION_ID;

@Slf4j
@Service
public class AuthorizationSessionServiceImpl implements AuthorizationSessionService {
    private final CacheService cacheService;

    private final AuthorizationSessionRepository authorizationSessionRepository;

    private final KafkaTemplate<String, String> tctKafkaTemplate;

    private final ObjectMapper xmlMapper;

    private final EventService eventService;

    private final EndAuthorizationEventStoreRepository endAuthorizationEventStoreRepository;

    private final EndAuthorizationResultService endAuthorizationResultService;

    private final DataReportHistoryRepository dataReportHistoryRepository;

    private final DifferentMessageReportRepository differentMessageReportRepository;

    private final FailedMessageReportRepository failedMessageReportRepository;


    @Value("${app.taxCode}")
    private String tvanTaxCode;

    @Value("${app.pban}")
    private String pBan;

    @Value("${app.tctTaxCode}")
    private String tctTaxCode;

    @Value("${spring.kafka.topics.direct-topics-in}")
    private String kafkaQueueIn;

    public AuthorizationSessionServiceImpl(CacheService cacheService,
                                           AuthorizationSessionRepository authorizationSessionRepository,
                                           @Qualifier("tctDirectKafkaTemplate") KafkaTemplate<String, String> tctKafkaTemplate,
                                           @Qualifier("xmlMapper") ObjectMapper xmlMapper,
                                           EventService eventService,
                                           EndAuthorizationEventStoreRepository endAuthorizationEventStoreRepository,
                                           EndAuthorizationResultService endAuthorizationResultService,
                                           DataReportHistoryRepository dataReportHistoryRepository,
                                           DifferentMessageReportRepository differentMessageReportRepository,
                                           FailedMessageReportRepository failedMessageReportRepository) {
        this.cacheService = cacheService;
        this.authorizationSessionRepository = authorizationSessionRepository;
        this.tctKafkaTemplate = tctKafkaTemplate;
        this.xmlMapper = xmlMapper;
        this.eventService = eventService;
        this.endAuthorizationEventStoreRepository = endAuthorizationEventStoreRepository;
        this.endAuthorizationResultService = endAuthorizationResultService;
        this.dataReportHistoryRepository = dataReportHistoryRepository;
        this.differentMessageReportRepository = differentMessageReportRepository;
        this.failedMessageReportRepository = failedMessageReportRepository;
    }

    @Override
    @Transactional
    public void saveValueInAuthorizationSession(String sessionID) {
        log.info("Start save value in authorization session: {}", sessionID);

        AuthorizeReconciliationResultModel authorizeReconciliationResultModel =
                endAuthorizationResultService.getDataForReconciliation(sessionID);
        log.info("authorizeReconciliationResultModel: {}", authorizeReconciliationResultModel);
        authorizeReconciliationResultModel.setStatus(AuthorizeSessionStatus.SYNCED.code());
        authorizeReconciliationResultModel.setSessionId(sessionID);

        authorizationSessionRepository.updateAuthorizationSession(authorizeReconciliationResultModel);
        log.info("End save value in authorization session");

    }

    @Override
    public void sendReconciliation() throws JsonProcessingException {
        int status = AuthorizeSessionStatus.SYNCED.code();
        List<AuthorizationSession> authorizeSessions =
                authorizationSessionRepository.getAuthorizationSessionList(status);
        log.info("Get list Authorization Session: {}", authorizeSessions);

        AuthorizeReconciliationResultModel authorizeReconciliationResultModel = getReconciliationResult(authorizeSessions);

        List<SLDSoat> sldSoats = new ArrayList<>();
        TDiep902 tDiep902 = createTDiep902(authorizeReconciliationResultModel, sldSoats);
        String tDiep902Str = xmlMapper.writeValueAsString(tDiep902);


        getMissingMessageWithTaxCode(authorizeSessions, authorizeReconciliationResultModel);

        String messageId = tDiep902.getTTChung().getMTDiep();
        MDC.put(TRANSACTION_ID, messageId);
        createEndAuthorizationEventStoreForMessage902(messageId);
        sendTDiep902(tDiep902Str, authorizeSessions, messageId, authorizeReconciliationResultModel, sldSoats);
    }


    private AuthorizeReconciliationResultModel getReconciliationResult(List<AuthorizationSession> authorizeSessions){
        int sumTotalMessage500 = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage500()))
                .mapToInt(AuthorizationSession::getTotalMessage500).sum();
        int sumTotalMessage503 = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage503()))
                .mapToInt(AuthorizationSession::getTotalMessage503).sum();
        int sumTotalMessage504 = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage504()))
                .mapToInt(AuthorizationSession::getTotalMessage504).sum();
        int sumTotalMessage999Of500Success = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage999Of500Success()))
                .mapToInt(AuthorizationSession::getTotalMessage999Of500Success).sum();
        int sumTotalMessage999Of503Success = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage999Of503Success()))
                .mapToInt(AuthorizationSession::getTotalMessage999Of503Success).sum();
        int sumTotalMessage999Of504Success = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage999Of504Success()))
                .mapToInt(AuthorizationSession::getTotalMessage999Of504Success).sum();
        int sumTotalMessage999Of500Error = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage999Of500Error()))
                .mapToInt(AuthorizationSession::getTotalMessage999Of500Error).sum();
        int sumTotalMessage999Of503Error = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage999Of503Error()))
                .mapToInt(AuthorizationSession::getTotalMessage999Of503Error).sum();
        int sumTotalMessage999Of504Error = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage999Of504Error()))
                .mapToInt(AuthorizationSession::getTotalMessage999Of504Error).sum();
        int sumTotalMessage1Of500 = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage1Of500()))
                .mapToInt(AuthorizationSession::getTotalMessage1Of500).sum();
        int sumTotalMessage1Of503 = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage1Of503()))
                .mapToInt(AuthorizationSession::getTotalMessage1Of503).sum();
        int sumTotalMessage1Of504 = authorizeSessions.stream()
                .filter(o -> Objects.nonNull(o.getTotalMessage1Of504()))
                .mapToInt(AuthorizationSession::getTotalMessage1Of504).sum();
        StringBuilder missingMessage500 = new StringBuilder();
        StringBuilder missingMessage503 = new StringBuilder();
        StringBuilder missingMessage504 = new StringBuilder();
        for(AuthorizationSession as : authorizeSessions ){
            String sessionId = as.getId();
            List<String> missMessage500 = endAuthorizationEventStoreRepository
                    .getMissingMessageForReconciliation(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_500_INT);

            getMissingMessage(missMessage500, missingMessage500);
            List<String> missMessage503 = endAuthorizationEventStoreRepository
                    .getMissingMessageForReconciliation(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_503_INT);
            getMissingMessage(missMessage503, missingMessage503);

            List<String> missMessage504 = endAuthorizationEventStoreRepository
                    .getMissingMessageForReconciliation(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_504_INT);
            getMissingMessage(missMessage504, missingMessage504);
        }
        return AuthorizeReconciliationResultModel.builder().sumTotalMessage500(sumTotalMessage500)
                .sumTotalMessage503(sumTotalMessage503).sumTotalMessage504(sumTotalMessage504)
                .sumTotalMessage999Of500Success(sumTotalMessage999Of500Success)
                .sumTotalMessage999Of503Success(sumTotalMessage999Of503Success)
                .sumTotalMessage999Of504Success(sumTotalMessage999Of504Success)
                .sumTotalMessage999Of500Error(sumTotalMessage999Of500Error)
                .sumTotalMessage999Of503Error(sumTotalMessage999Of503Error)
                .sumTotalMessage999Of504Error(sumTotalMessage999Of504Error)
                .sumTotalMessage1Of500(sumTotalMessage1Of500).sumTotalMessage1Of503(sumTotalMessage1Of503)
                .sumTotalMessage1Of504(sumTotalMessage1Of504).missingMessage500(missingMessage500.toString())
                .missingMessage503(missingMessage503.toString()).missingMessage504(missingMessage504.toString()).build();
    }

    private void getMissingMessage(List<String> missMessage, StringBuilder missingMessage) {
        if (!CollectionUtils.isEmpty(missMessage)) {
            if(missingMessage.length() == 0){
                missingMessage.append(String.join(";", missMessage));
            }else {
                missingMessage.append(";").append(String.join(";", missMessage));
            }
        }
    }

    private void getMissingMessageWithTaxCode(List<AuthorizationSession> authorizeSessions,
                                              AuthorizeReconciliationResultModel authorizeReconciliationResultModel) {
        StringBuilder missingMessage500 = new StringBuilder();
        StringBuilder missingMessage503 = new StringBuilder();
        StringBuilder missingMessage504 = new StringBuilder();
        for(AuthorizationSession as : authorizeSessions ){
            String sessionId = as.getId();
            List<String> missMessage500 = endAuthorizationEventStoreRepository
                    .getMissingMessageForReconciliationWithTaxCode(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_500_INT);

            getMissingMessage(missMessage500, missingMessage500);
            List<String> missMessage503 = endAuthorizationEventStoreRepository
                    .getMissingMessageForReconciliationWithTaxCode(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_503_INT);
            getMissingMessage(missMessage503, missingMessage503);

            List<String> missMessage504 = endAuthorizationEventStoreRepository
                    .getMissingMessageForReconciliationWithTaxCode(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_504_INT);
            getMissingMessage(missMessage504, missingMessage504);
        }
        authorizeReconciliationResultModel.setMissingMessage500(missingMessage500.toString());
        authorizeReconciliationResultModel.setMissingMessage503(missingMessage503.toString());
        authorizeReconciliationResultModel.setMissingMessage504(missingMessage504.toString());

    }

    private Instant getFirstMomentOfToday() {
        Instant date = Instant.now();
        ZonedDateTime utc = date.atZone(ZoneId.of("UTC"));
        ZonedDateTime zonedDateTime = utc.withHour(0)
                .withMinute(0)
                .withSecond(0);
        return zonedDateTime.toInstant();
    }

    private void sendTDiep902(String tDiep902Str, List<AuthorizationSession> authorizationSessions,
                              String messageId, AuthorizeReconciliationResultModel authorizeReconciliationResultModel,
                              List<SLDSoat> sldSoats) {
        log.info("Send message 902 to TCT start: {}", tDiep902Str);

        RequestInfo requestInfo = RequestInfo.builder()
                .createdAt(Instant.now())
                .initAt(Instant.now())
                .appId(tvanTaxCode)
                .taxCode(tvanTaxCode)
                .transactionId(messageId)
                .messageType(MessageType._902_ThongDiepBaoCaoDoiSoat)
                .build();
        cacheService.saveRequestInfo(requestInfo);

        ListenableFuture<SendResult<String, String>> listenableFuture =
                tctKafkaTemplate.send(kafkaQueueIn, tDiep902Str);
        listenableFuture.addCallback(new MdcListenableFutureCallback<>(
                result -> handleSend902Success(authorizationSessions, messageId, tDiep902Str,
                        authorizeReconciliationResultModel, sldSoats),
                ex -> handleSend902Failure(authorizationSessions, messageId, tDiep902Str, ex)
        ));
    }

    private TDiep902 createTDiep902(AuthorizeReconciliationResultModel authorizeReconciliationResultModel,
                                    List<SLDSoat> sldSoats) {
        log.info("Start create message 902");
        String mTDiep =  tvanTaxCode + Utils.upperCaseUUIDWithoutDash();
        TTChung ttChung = new TTChung().PBan(pBan)
                .MNGui(tvanTaxCode)
                .MNNhan(tctTaxCode)
                .MLTDiep(MessageType._902_ThongDiepBaoCaoDoiSoat.code())
                .MTDiep(mTDiep);

        List<SLDSoat> lst = new ArrayList<>();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String date = df.format(Date.from(Instant.now()));

        SLDSoat sldSoat500 = createSLDSoat(Constant.AUTHORIZATION_SESSION.MESSAGE_500, date,
                authorizeReconciliationResultModel.getSumTotalMessage500(),
                authorizeReconciliationResultModel.getSumTotalMessage999Of500Success(),
                authorizeReconciliationResultModel.getSumTotalMessage999Of500Error(),
                authorizeReconciliationResultModel.getSumTotalMessage1Of500(),
                authorizeReconciliationResultModel.getMissingMessage500());
        lst.add(sldSoat500);

        SLDSoat sldSoat503 = createSLDSoat(Constant.AUTHORIZATION_SESSION.MESSAGE_503, date,
                authorizeReconciliationResultModel.getSumTotalMessage503(),
                authorizeReconciliationResultModel.getSumTotalMessage999Of503Success(),
                authorizeReconciliationResultModel.getSumTotalMessage999Of503Error(),
                authorizeReconciliationResultModel.getSumTotalMessage1Of503(),
                authorizeReconciliationResultModel.getMissingMessage503());
        lst.add(sldSoat503);

        SLDSoat sldSoat504 = createSLDSoat(Constant.AUTHORIZATION_SESSION.MESSAGE_504, date,
                authorizeReconciliationResultModel.getSumTotalMessage504(),
                authorizeReconciliationResultModel.getSumTotalMessage999Of504Success(),
                authorizeReconciliationResultModel.getSumTotalMessage999Of504Error(),
                authorizeReconciliationResultModel.getSumTotalMessage1Of504(),
                authorizeReconciliationResultModel.getMissingMessage504());
        lst.add(sldSoat504);
        sldSoats.addAll(lst);
        DLieu902 dLieu902 = new DLieu902().DSSLDSoat(lst);
        log.info("End create message 902 {}", dLieu902);
        return new TDiep902()
                .TTChung(ttChung)
                .DLieu(dLieu902);
    }

    private SLDSoat createSLDSoat(String mltDiep, String date, int sumTotalMessage, int sumTotalMessage999Success,
                                  int sumTotalMessage999Error, int sumTotalMessage1, String missingMessage) {
        List<DSPHoi> dspHoiList = new ArrayList<>();
        int slgNhan = sumTotalMessage999Success + sumTotalMessage999Error + sumTotalMessage1;
        int slgLech = Math.abs(sumTotalMessage - slgNhan);
        DSPHoi dspHoi999_1 = new DSPHoi().MLPHoi(Constant.AUTHORIZATION_SESSION.MLPHoi_999_1)
                .SLGNhan(slgNhan)
                .SLGLech(slgLech)
                .DSTDLech(Arrays.asList(missingMessage.split(";")));

        List<SLNCTiet> slncTietList = new ArrayList<>();
        SLNCTiet slncTiet0 = new SLNCTiet().TTPHoi(Constant.AUTHORIZATION_SESSION.TTPHoi_0)
                .SLuong(sumTotalMessage999Success);
        SLNCTiet slncTiet1 = new SLNCTiet().TTPHoi(Constant.AUTHORIZATION_SESSION.TTPHoi_1)
                .SLuong(sumTotalMessage999Error);
        slncTietList.add(slncTiet0);
        slncTietList.add(slncTiet1);
        DSPHoi dspHoi999 = new DSPHoi().MLPHoi(Constant.AUTHORIZATION_SESSION.MLPHoi_999)
                .SLGNhan(sumTotalMessage999Success+sumTotalMessage999Error)
                .SLGLech(0)
                .DSSLNCTiet(slncTietList);

        DSPHoi dspHoi1 = new DSPHoi().MLPHoi(Constant.AUTHORIZATION_SESSION.MLPHoi_1)
                .SLGNhan(sumTotalMessage1)
                .SLGLech(0);

        dspHoiList.add(dspHoi999_1);
        dspHoiList.add(dspHoi999);
        dspHoiList.add(dspHoi1);

        return new SLDSoat().MLTDiep(mltDiep)
                .Ngay(date)
                .SLDGui(sumTotalMessage)
                .DSPHLech(dspHoiList);
    }

    @Override
    public List<String> getListTopicsToSyncMessage() {
        Instant time = getFirstMomentOfToday();
        List<String> allTopicsBeforeToday = authorizationSessionRepository.getTopicsToSyncMessage(time);
        List<String> allTopicsUntilNow = authorizationSessionRepository.getTopicsToSyncMessage(Instant.now());

        /*
        Thời điểm chạy job là 17h05 ngày T
        Nếu trước 0h00 ngày T, có các phiên ủy quyền chưa đc đối soát :
            - Nếu trong ngày T ko có phiên ủy quyền nào -> chỉ đối soát cho các phiên trước 0h00 ngày T
            - Nếu trong ngày T có phiên ủy quyền (kết thúc trước thời điểm chạy job) -> đối soát cho các phiên trước 0h00 ngày T và cả phiên của ngày T
        Nếu trước 0h00 ngày T, ko có các phiên ủy quyền chưa được đối soát -> ko cần đối soát, để qua ngày T+1 lặp lại logic này
         */
        if (!allTopicsBeforeToday.isEmpty()) {
            if (allTopicsBeforeToday.size() < allTopicsUntilNow.size()) {
                return allTopicsUntilNow;
            }
            return allTopicsBeforeToday;
        }

        return Collections.emptyList();

    }

    private void handleSend902Success(List<AuthorizationSession> authorizeSessions, String messageId,
                                      String xml, AuthorizeReconciliationResultModel authorizeReconciliationResultModel,
                                      List<SLDSoat> sldSoats) {
        log.info("Send message 902 to tct success");
        authorizeSessions.forEach(session -> {
            session.setStatus(AuthorizeSessionStatus.SENT_902_SUCCESS.code());
            session.setMessageId902(messageId);
        });
        authorizationSessionRepository.saveAll(authorizeSessions);

        eventService.push(Event.builder()
                .createdAt(Instant.now())
                .initAt(Instant.now())
                .transactionId(messageId)
                .appId(tvanTaxCode)
                .taxCode(tvanTaxCode)
                .messageType(MessageType._902_ThongDiepBaoCaoDoiSoat)
                .messageId(messageId)
                .type(EventType.SEND_902_MESSAGE_TO_TCT_SUCCESS)
                .data(xml)
                .build());

        //save data to DATA_REPORT_HISTORY and DIFFERENT_MESSAGE_REPORT
        saveValueToDataReportHistory(messageId, authorizeReconciliationResultModel, sldSoats, authorizeSessions);

        log.info("Update list Authorization Session: {}", authorizeSessions);
    }

    private void saveValueToDataReportHistory(String reportMessageId,
                                              AuthorizeReconciliationResultModel authorizeReconciliationResultModel,
                                              List<SLDSoat> sldSoats, List<AuthorizationSession> authorizeSessions) {
        log.info("Start save value in data Report Histories");
        List<DataReportHistory> dataReportHistories = new ArrayList<>();
        for (SLDSoat sldSoat : sldSoats){
            if (sldSoat.getDSPHLech() != null) {
                sldSoat.getDSPHLech().forEach(dsp -> dataReportHistories.add(DataReportHistory.builder()
                        .reportDate(LocalDate.now())
                        .reportMessageId(reportMessageId)
                        .messageType(Integer.valueOf(sldSoat.getMLTDiep()))
                        .totalSent(sldSoat.getSLDGui())
                        .responseType(dsp.getMLPHoi())
                        .totalReceived(dsp.getSLGNhan())
                        .totalDeviation(dsp.getSLGLech()).build()));
            }
        }
        dataReportHistoryRepository.saveAll(dataReportHistories);
        log.info("End save value in data Report Histories");

        log.info("Start save value in Different Message Report");
        List<DifferentMessageReport> differentMessageReportList = new ArrayList<>();
        getDifferentMessageReportList(differentMessageReportList,
                authorizeReconciliationResultModel.getMissingMessage500(),
                reportMessageId,
                Constant.AUTHORIZATION_SESSION.MESSAGE_500_INT);
        getDifferentMessageReportList(differentMessageReportList,
                authorizeReconciliationResultModel.getMissingMessage503(),
                reportMessageId,
                Constant.AUTHORIZATION_SESSION.MESSAGE_503_INT);
        getDifferentMessageReportList(differentMessageReportList,
                authorizeReconciliationResultModel.getMissingMessage504(),
                reportMessageId,
                Constant.AUTHORIZATION_SESSION.MESSAGE_504_INT);
        differentMessageReportRepository.saveAll(differentMessageReportList);
        log.info("End save value in Different Message Report");


        log.info("Start save value in Not Success Message Report");
        List<FailedMessageReport> failedMessageReports =
                getListFailedMessage(authorizeSessions, reportMessageId);

        failedMessageReportRepository.saveAll(failedMessageReports);
        log.info("End save value in Not Success Message Report");
    }

    private List<FailedMessageReport> getListFailedMessage(List<AuthorizationSession> authorizeSessions,
                                                           String reportMessageId){
        List<FailedMessageReport> failedMessageReports = new ArrayList<>();
        for(AuthorizationSession as : authorizeSessions ){
            String sessionId = as.getId();
            List<EndAuthorizationProjection> failedMessage999Of500s = endAuthorizationEventStoreRepository
                    .getListMessageWasNotAccepted(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_500_INT);
            addFailMessageToListEntity(failedMessageReports, failedMessage999Of500s, reportMessageId);

            List<EndAuthorizationProjection> failedMessage999Of503 = endAuthorizationEventStoreRepository
                    .getListMessageWasNotAccepted(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_503_INT);
            addFailMessageToListEntity(failedMessageReports, failedMessage999Of503, reportMessageId);

            List<EndAuthorizationProjection> failedMessage999Of504 = endAuthorizationEventStoreRepository
                    .getListMessageWasNotAccepted(sessionId, Constant.AUTHORIZATION_SESSION.MESSAGE_504_INT);
            addFailMessageToListEntity(failedMessageReports, failedMessage999Of504, reportMessageId);
        }
        return failedMessageReports;
    }

    private void addFailMessageToListEntity(List<FailedMessageReport> failedMessageReports,
                                            List<EndAuthorizationProjection> failedMessages, String reportMessageId){
        for (EndAuthorizationProjection failedMessage : failedMessages){
            if (failedMessage != null) {
                failedMessageReports.add(FailedMessageReport.builder()
                        .reportDate(LocalDate.now())
                        .reportMessageId(reportMessageId)
                        .messageType(failedMessage.getMessageType())
                        .status(0)
                        .messageId(failedMessage.getMessageId())
                        .taxCode(failedMessage.getTaxCode()).build());
            }
        }
    }

    private void getDifferentMessageReportList(List<DifferentMessageReport> differentMessageReportList,
                                               String missingMessage, String reportMessageId, Integer messageType){
        if(!StringUtils.hasLength(missingMessage)) return;
        List<String> missingMessageAndTaxCodes =
                Arrays.asList(missingMessage.split(";"));
        if(CollectionUtils.isEmpty(missingMessageAndTaxCodes)) return;
        for (String message : missingMessageAndTaxCodes){
            differentMessageReportList.add(DifferentMessageReport.builder()
                    .reportMessageId(reportMessageId)
                    .messageType(messageType)
                    .missingMessageId(String.valueOf(message.split("\\+")[0]))
                    .taxCode(String.valueOf(message.split("\\+")[1]))
                    .reportDate(LocalDate.now())
                    .build());
        }


    }

    private void handleSend902Failure(List<AuthorizationSession> authorizeSessions, String messageId, String xml, Throwable ex) {
        log.error("Send message 902 to tct fail", ex);
        authorizeSessions.forEach(session -> session.setStatus(AuthorizeSessionStatus.SENT_902_FAIL.code()));
        authorizationSessionRepository.saveAll(authorizeSessions);

        eventService.push(Event.builder()
                .createdAt(Instant.now())
                .initAt(Instant.now())
                .transactionId("AUTHORIZE_SYNC_MESSAGE")
                .appId(tvanTaxCode)
                .taxCode(tvanTaxCode)
                .messageType(MessageType._902_ThongDiepBaoCaoDoiSoat)
                .messageId(messageId)
                .type(EventType.SEND_902_MESSAGE_TO_TCT_FAILED)
                .data(xml)
                .build());

        log.info("Update list Authorization Session: {}", authorizeSessions);
    }

    private void createEndAuthorizationEventStoreForMessage902(String messageId){
        endAuthorizationEventStoreRepository.save(EndAuthorizationEventStore.builder()
                .messageId(messageId)
                .messageType(MessageType._902_ThongDiepBaoCaoDoiSoat.code())
                .refTransactionId(messageId)
                .refMessageType(MessageType._902_ThongDiepBaoCaoDoiSoat.code())
                .refInitAt(Instant.now())
                .initAt(Instant.now())
                .status(EndAuthorizationEventStatus.INIT.value())
                .refTaxCode(tvanTaxCode)
                .build());
    }


}
