package vn.vnpay.tvan.apps.reconciliation.service;

import vn.vnpay.tvan.libs.common.constant.EventType;
import vn.vnpay.tvan.libs.common.model.Request;
import vn.vnpay.tvan.libs.common.model.entity.EventStore;

import java.io.InputStream;

public interface MinioService {
    //Lấy ra dữ liệu dạng InputStream của 1 object lưu trong min.io dựa theo path
    InputStream get(String path);

    //Lấy ra dữ liệu được lưu trong min.io của 1 transaction dựa theo event type
    String getData(EventStore eventStore, EventType eventType);

    //L<PERSON>y thông điệp từ min.io
    Request getMessageFromMinIO(EventStore eventStore, EventType eventType);
}
