package vn.vnpay.tvan.apps.reconciliation.model;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@ToString
@Entity
@Table(name = "TOTAL_MESSAGE_REPORT")
public class TotalMessageReport {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "REPORT_DATE")
    private Date reportDate;

    @Column(name = "REQUEST_TYPE")
    private Integer requestType;

    @Column(name = "RESPONSE_TYPE")
    private Integer responseType;

    @Column(name = "TOTAL")
    private Integer total;
}
