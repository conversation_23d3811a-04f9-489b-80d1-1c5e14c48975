package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import vn.vnpay.tvan.apps.reconciliation.repository.EmailHistoryRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.ErrorWarningRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.TransactionErrorRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.UserErrorWarningRepository;
import vn.vnpay.tvan.apps.reconciliation.service.EmailService;
import vn.vnpay.tvan.apps.reconciliation.service.ErrorWarningService;
import vn.vnpay.tvan.libs.common.model.entity.EmailHistory;
import vn.vnpay.tvan.libs.common.model.entity.ErrorWarning;
import vn.vnpay.tvan.libs.common.model.entity.TransactionError;
import vn.vnpay.tvan.libs.common.model.entity.UserErrorWarning;

import javax.mail.MessagingException;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.time.Instant.now;
import static java.util.Objects.nonNull;
import static vn.vnpay.tvan.apps.reconciliation.constant.Constant.EMAIL_SENDER;
import static vn.vnpay.tvan.apps.reconciliation.constant.TemplateEmail.ERROR_WARNING_EMAIL_TEMPLATE;
import static vn.vnpay.tvan.libs.common.constant.Constant.DATE_FORMAT.DATE_TIME;
import static vn.vnpay.tvan.libs.common.constant.EmailHistoryType.ERROR_WARNING;
import static vn.vnpay.tvan.libs.common.util.DatetimeUtil.HCM_ZONE_ID;
import static vn.vnpay.tvan.libs.common.util.DatetimeUtil.convertInstantToStringWithDefaultZone;
import static vn.vnpay.tvan.libs.common.util.DatetimeUtil.convertStringToInstant;
import static vn.vnpay.tvan.libs.common.util.Utils.nonNullOrEmpty;

@Slf4j
@Service
public class ErrorWarningServiceImpl implements ErrorWarningService {
    private final TransactionErrorRepository transactionErrorRepository;
    private final ErrorWarningRepository errorWarningRepository;
    private final UserErrorWarningRepository userErrorWarningRepository;
    private final EmailHistoryRepository emailHistoryRepository;
    private final EmailService emailService;
    private final RedisTemplate<String, String> redisTemplate;
    String LAST_TIME_WANING_ID = "ERROR_WARNING_LAST_SENT";

    public ErrorWarningServiceImpl(ErrorWarningRepository errorWarningRepository,
                                   UserErrorWarningRepository userErrorWarningRepository,
                                   TransactionErrorRepository transactionErrorRepository,
                                   EmailHistoryRepository emailHistoryRepository,
                                   EmailService emailService,
                                   RedisTemplate<String, String> redisTemplate) {
        this.errorWarningRepository = errorWarningRepository;
        this.userErrorWarningRepository = userErrorWarningRepository;
        this.transactionErrorRepository = transactionErrorRepository;
        this.emailHistoryRepository = emailHistoryRepository;
        this.emailService = emailService;
        this.redisTemplate = redisTemplate;
    }


    @Override
    public void errorWarning() {
        try {
            Map<String, String> errorCodeMap = new HashMap<>();
            List<EmailHistory> emailHistoryList = new ArrayList<>();

            log.info("Checking for errors time = " + Instant.now());

            log.info("Getting error warnings with active status");
            List<ErrorWarning> errorWarnings = errorWarningRepository.getAllActiveStatus();
            log.info("Error warnings with active status size = " + errorWarnings.size());

            List<ErrorWarning> errorWarningNeedToUpdatedLastWarning = new ArrayList<>();

            if (nonNullOrEmpty(errorWarnings)) {
                errorWarnings.forEach(x -> {
                    log.info(MessageFormat.format("Request to check error warning when errorCode = {0} " +
                                    "error {1} times in {2} minutes",
                            x.getErrorCode(), x.getNumberOfErrors(), x.getErrorWarningPeriod()));

                    log.info(MessageFormat.format("Started getting transaction with errorCode = {0}",
                            x.getErrorCode()));

                    Instant lastWarning = getLastTimeWarning(x);

                    log.info(MessageFormat.format("Last time warning of error code = {0} is {1}",
                            x.getErrorCode(), lastWarning));

                    List<TransactionError> transactionErrors =
                            transactionErrorRepository.getAllByErrorCode(x.getErrorCode(), lastWarning);

                    log.info(MessageFormat.format("Found {0} transaction with errorCode = {1}",
                            transactionErrors.size(), x.getErrorCode()));


                    if (nonNullOrEmpty(transactionErrors)) {
                        /*TH1: Nếu cấu hình cảnh báo lỗi khi xuất hiện 1 lỗi trong n phút, thì khi xuất hiện 1 lỗi,
                         * lưu lại last time warning lần tới kiểm tra*/
                        if (transactionErrors.size() == 1
                                && transactionErrors.get(0).getCreatedAt().isAfter(lastWarning)) {
                            log.info("Transaction errors size = 1, last = " + transactionErrors.get(0).getCreatedAt());
                            enrichLastTimeCalling(transactionErrors.get(0), errorCodeMap, x);
                        }

                        /*TH2: Nếu có từ 2 bản ghi lỗi trở lên -> thuật toán gom 2 bản ghi gần nhau lại tính khoảng thời
                        * gian lỗi giữa 2 bản ghi và cộng lại đến khi hết mảng lỗi, cuối cùng nhận được số bản ghi lỗi
                        * trong khoảng thời gian đã cấu hình*/
                        if (transactionErrors.size() > 1) {
                            log.info("Transaction errors's size > 1");
                            /*Do tính số lượng lỗi theo cặp nên số lưng lỗi sẽ bắt đầu tại 1*/
                            int errorNumber = 1;
                            long minutes = 0;

                            for (int i = 0; i < transactionErrors.size() - 1; i++) {
                                TransactionError indexA = transactionErrors.get(i);
                                TransactionError indexB = transactionErrors.get(i + 1);

                                minutes += ChronoUnit.MINUTES.between(indexB.getCreatedAt(), indexA.getCreatedAt());

                                /*Nếu thời gian vượt quá thời gian config
                                hoặc kết thúc vòng lặp ( i = transactionErrors.size() - 2)
                                mà vẫn trong khoảng thời gian thì lưu last time calling và break vòng lặp
                                để kiểm tra số lượng lỗi trong khoảng thời gian đó  */

                                if (minutes >= x.getErrorWarningPeriod() || i == transactionErrors.size() - 2) {
                                    enrichLastTimeCalling(indexA, errorCodeMap, x);
                                    break;
                                }
                                errorNumber += 1;
                            }

                            if (errorNumber >= x.getNumberOfErrors()) {
                                log.info(MessageFormat.format("ErrorCode = {0} error {1} times in {2} minutes",
                                        x.getErrorCode(), transactionErrors.size(), minutes));

                                log.info("Started enriching errorWarning that needed email, id = " + x.getId()
                                        + "errorCode = " + x.getErrorCode());
                                sendMailWarning(x, emailHistoryList);
                            }
                        }
                        errorWarningNeedToUpdatedLastWarning.add(x);
                    }
                });

            redisTemplate.opsForHash().putAll(LAST_TIME_WANING_ID, errorCodeMap);

            saveWarningEmailHistory(emailHistoryList);
            saveLastTimeCalling(errorWarningNeedToUpdatedLastWarning);
        }
    } catch(Exception e){
        log.info("An error occurred while error warning, error :" + e.getMessage());
    }
}

    private void enrichLastTimeCalling(TransactionError trans, Map<String, String> errorCodeMap, ErrorWarning errorWarning) {
        log.info("Prepared enriching last warning date time to save into db, last time calling = " + trans.getCreatedAt());
        errorWarning.setLastTimeWarning(trans.getCreatedAt());

        String lastTimeWarning = convertInstantToStringWithDefaultZone(
                trans.getCreatedAt(),
                DATE_TIME,
                HCM_ZONE_ID
        );
        log.info("Prepared caching last warning date time = " + lastTimeWarning);

        errorCodeMap.put(trans.getErrorCode(), lastTimeWarning);
    }

    private void saveWarningEmailHistory(List<EmailHistory> emailHistoryList) {
        if (nonNullOrEmpty(emailHistoryList)) {
            log.info("Saving warning email history!");
            emailHistoryRepository.saveAll(emailHistoryList);
        }
    }

    private void saveLastTimeCalling(List<ErrorWarning> errorWarningNeedToUpdatedLastWarning) {
        if(nonNullOrEmpty(errorWarningNeedToUpdatedLastWarning)){
            log.info("Updating warning last time");
            errorWarningRepository.saveAll(errorWarningNeedToUpdatedLastWarning);
        }
    }

    private Instant getLastTimeWarning(ErrorWarning errorWarning) {
        log.info("Started getting last time warning or else return default");
        Map<Object, Object> cachedLastWarning = redisTemplate.opsForHash().entries(LAST_TIME_WANING_ID);

        Object lastWarningCached = cachedLastWarning.get(errorWarning.getErrorCode());

        return nonNull(lastWarningCached) ?
                convertStringToInstant(lastWarningCached.toString()) :
                getLastTimeWarningByWarningId(errorWarning.getId());
    }

    private Instant getLastTimeWarningByWarningId(Long warningId) {
        return errorWarningRepository.findById(warningId).orElseThrow().getLastTimeWarning();
    }

    private void sendMailWarning(ErrorWarning errorWarning, List<EmailHistory> emailHistoryList) {
        List<UserErrorWarning> userErrorWarnings = userErrorWarningRepository.
                findAllByErrorWarningId(errorWarning.getId());

        List<String> listEmail = userErrorWarnings.stream().map(UserErrorWarning::getEmail).toList();
        log.info("Started to send email error warning, size = " + listEmail.size());

        String[] emails = listEmail.toArray(new String[0]);
        Context context = new Context();
        context.setVariable("emailContent", errorWarning.getEmailContent());

        try {
            Instant sendTime = now();
            log.info("Send mail time = {}", sendTime);
            emailService.sendHtmlMailToGroup(emails, ERROR_WARNING_EMAIL_TEMPLATE, errorWarning.getEmailTitle(), context);

            enrichEmailHistory(emailHistoryList, sendTime, errorWarning);
        } catch (MessagingException e) {
            log.info("Send email error: ", e);
        }
    }

    private void enrichEmailHistory(List<EmailHistory> emailHistoryList, Instant sendTime, ErrorWarning errorWarning) {
        log.info("Started enriching email history");

        EmailHistory history = new EmailHistory();
        history.setType(ERROR_WARNING);
        history.setSentBy(EMAIL_SENDER);
        history.setSentAt(sendTime);
        history.setDescription("Gửi email cảnh báo lỗi");
        history.setReferenceId(errorWarning.getId());
        history.setCustomerCode(errorWarning.getErrorCode());
        emailHistoryList.add(history);
    }
}
