package vn.vnpay.tvan.apps.reconciliation.model;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "EMAIL_CONFIG")
@Data
@ToString
public class EmailConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "NAME")
    private String name;

    @Column(name = "SUBJECT", length = 1000)
    private String subject;

    @Column(name = "BODY", length = 1000)
    private String body;

    @Column(name = "EMAIL_TO", length = 1000)
    private String emailTo;

    @Column(name = "EMAIL_CC", length = 1000)
    private String emailCc;

    @Column(name = "USERNAME")
    private String username;

    @Column(name = "PASSWORD")
    private String password;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "IS_ACTIVE")
    private Boolean isActive;

}
