package vn.vnpay.tvan.apps.reconciliation.service;

import org.thymeleaf.context.Context;
import vn.vnpay.tvan.apps.reconciliation.constant.TemplateEmail;

import javax.activation.DataSource;
import javax.mail.MessagingException;


public interface EmailService {
    boolean sendMailWithAttachment(String fileName, DataSource dataSource);

    void sendHtmlMail(String to, TemplateEmail templateEmail, Context context) throws MessagingException;
    void sendHtmlMailToGroup(String[] to, TemplateEmail templateEmail, String subject, Context context) throws MessagingException;
    void sendHtmlMailToGroup(String[] to, TemplateEmail templateEmail, Context context) throws MessagingException;
}
