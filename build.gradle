import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot' version '2.6.6' apply false
    id 'io.spring.dependency-management' version '1.0.11.RELEASE' apply false
    id 'java'
    // id "org.owasp.dependencycheck" version "8.2.1"
}


group 'vn.vnpay.tvan'
version '2.7.0'
compileJava.options.encoding = 'UTF-8'

repositories {
    mavenCentral()
}

def getEnv = { ->
    return System.properties['env'] ?: 'dev'
}

ext {

    isProd = getEnv() == 'prod'
    isTest = getEnv() == 'test'
    isStaging = getEnv() == 'staging'
    isDev = getEnv() == 'dev'


    lombokVersion = "1.18.22"
    jacksonVersion = '2.13.2'
    javaValidationVersion = "2.0.1.Final"
    swaggerAnnotationVersion = "2.1.13"
    jacksonDatabindNullableVersion = "0.2.2"
    javaxValidationVersion = "2.0.1.Final"
    jakartaXmlBindVersion = "3.0.1"
    javaxAnnotationVersion = "1.3.2"
    jaxbApiVersion = "2.3.1"
    glassfishJaxbVersion = "2.3.6"
    javaxServletVersion = "4.0.1"
    bouncycastleVersion = "1.70"
    guavaVersion = "31.1-jre"
    xmlsecVersion = "2.1.6"
    persistenceApiVersion = "2.2"
    xmlUnitVersion = "2.9.0"
    mapstructVersion = "1.4.2.Final"
    kafkaVersion = "3.1.0"
    minioVersion = "8.2.2"
    micrometerPrometheusVersion = "1.9.0"
    springCloudVersion = "2021.0.3"
    junitVersion = "5.8.2"
    openApiUiVersion = "1.6.6"
    jwtVersion = "0.2"
    aopVersion = "2.7.0"
    thymleafVersion = "2.7.0"
    okHttpVersion = "4.10.0"
    grayLogVersion = "1.1.0"
    jsonVersion = "20220320"
    hibernateTypesVersion = "2.17.3"
    hibernateCoreVersion = "5.6.7.Final"
    simpleCaptchaVersion = "1.2.2"
    commonLangVersion = "3.12.0"
    commonIoVersion = "2.11.0"
    efinLoggingVersion = "1.0.0-alpha20240402134229"
    httpClientVersion = "5.2.1"
    liquibaseVersion = "4.8.0"
    awsVersion = "1.12.661"

    if (isProd) {
        nd1450MessageVersion = "2.0.0"
    } else {
        nd1450MessageVersion = "2.0.0-alpha.81"
    }

}

def springProjects = [
        project(":apps:api"),
        project(":apps:handler"),
        project(":apps:etl"),
        project(":tools:fake-tct"),
        project(":apps:backoffice"),
        project(":apps:reconciliation-tct"),
        project(":tools:digital-signature"),
        project(":apps:portal"),
        project(":apps:config-server"),
        project(":apps:declaration"),
        project(":tools:resend-message")
]
configure(springProjects) {
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'java'

    sourceCompatibility = '17'

    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }
    
    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        }
    }

    dependencies {
        implementation project(':libs:common')
        implementation 'org.springframework.boot:spring-boot-starter-web'
        developmentOnly 'org.springframework.boot:spring-boot-devtools'
        testImplementation 'org.springframework.boot:spring-boot-starter-test'

        compileOnly "org.projectlombok:lombok"
        annotationProcessor "org.projectlombok:lombok"

        testCompileOnly "org.projectlombok:lombok"
        testAnnotationProcessor "org.projectlombok:lombok"
    }
}

def springLibProjects = [
        project(":libs:security"),
        project(":libs:common")
]
configure(springLibProjects) {

    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'java'
    apply plugin: 'maven-publish'

    dependencyManagement {
        imports {
            mavenBom SpringBootPlugin.BOM_COORDINATES
        }
    }
    publishing {
        publications{
            maven(MavenPublication){
                from components.java
            }
        }
        repositories {
            maven {
                credentials {
                    username NEXUS_USER
                    password NEXUS_PASSWORD
                }
                url "https://artifact.vnpay.vn/nexus/repository/efin-maven/"
                allowInsecureProtocol = true
            }
        }
    }
    dependencies {
        compileOnly "org.projectlombok:lombok"
        annotationProcessor "org.projectlombok:lombok"

        testCompileOnly "org.projectlombok:lombok"
        testAnnotationProcessor "org.projectlombok:lombok"
    }
}

subprojects {
    repositories {
        mavenCentral()
        maven {
            credentials {
                username NEXUS_USER
                password NEXUS_PASSWORD
            }
            url "https://artifact.vnpay.vn/nexus/repository/efin-maven/"
            allowInsecureProtocol = true
        }
    }

    task setProdEnv {
        System.setProperty("env", "prod")
    }

    task bootJarProd(type: GradleBuild) {
        group 'build'
        tasks = ['clean', 'setProdEnv', 'bootJar']
    }

}

// Add bump version to all project
allprojects {
    // apply plugin: 'org.owasp.dependencycheck'

    // dependencyCheck {
    //     formats=["JSON","HTML"]
    // }


    tasks.addRule("Pattern: bump<TYPE>Version") { String taskName ->
        if (taskName.matches("bump(Major|Minor|Patch)Version")) {
            task(taskName) {
                doLast {
                    String type = (taskName - 'bump' - 'Version')

                    println "Bumping ${type.toLowerCase()} version…"

                    String oldVersionName = version

                    Version v = new Version(oldVersionName)
                    v."bump$type"()

                    String newVersionName = v.getName()

                    println "$oldVersionName → $newVersionName"

                    def updated = buildFile.getText()
                    updated = updated.replaceFirst("version '$oldVersionName'", "version '$newVersionName'")
                    updated = updated.replaceFirst("version = '$oldVersionName'", "version = '$newVersionName'")
                    buildFile.setText(updated)
                }
            }
        }
    }
}
class Version {
    private int major
    private int minor
    private int patch

    Version(String version) {

        def (major, minor, patch) = version.tokenize('.')
        this.major = major.toInteger()
        this.minor = minor.toInteger()
        this.patch = patch.toInteger()
    }

    @SuppressWarnings("unused")
    void bumpMajor() {
        major += 1
        minor = 0
        patch = 0
    }

    @SuppressWarnings("unused")
    void bumpMinor() {
        minor += 1
        patch = 0
    }

    @SuppressWarnings("unused")
    void bumpPatch() {
        patch += 1
    }
    String getName() { "$major.$minor.$patch" }
}
