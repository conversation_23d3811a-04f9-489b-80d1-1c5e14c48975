
group = 'vn.vnpay.tvan.apps'
version = '2.7.0'
sourceCompatibility = '17'

dependencies {
	implementation 'org.springframework.cloud:spring-cloud-config-server'
    implementation 'org.springframework.boot:spring-boot-starter-security'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation "de.siegmar:logback-gelf:${grayLogVersion}"
}

tasks.named('test') {
	useJUnitPlatform()
}
task protectJar(type: Exec) {
    dependsOn build

    workingDir "${rootDir}".toString()

    commandLine 'cmd', '/c', 'java', '-jar', 'libs/stringer.jar',
            '-configFile', 'stringer.xml',
            'apps/config-server/build/libs/config-server-' << getVersion() << '.jar',
            'apps/config-server/build/libs/tvan-config-protected-'<< getVersion() << '.jar'

    println("Protected Jar file!")
}
