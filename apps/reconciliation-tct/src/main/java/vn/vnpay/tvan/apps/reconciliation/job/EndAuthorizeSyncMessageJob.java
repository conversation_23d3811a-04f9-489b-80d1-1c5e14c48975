package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerEndpoint;
import org.springframework.kafka.config.MethodKafkaListenerEndpoint;
import org.springframework.kafka.event.ListenerContainerIdleEvent;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.TopicPartitionOffset;
import org.springframework.messaging.handler.annotation.support.DefaultMessageHandlerMethodFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vn.vnpay.tvan.apps.reconciliation.service.AuthorizationSessionService;
import vn.vnpay.tvan.apps.reconciliation.service.CacheService;
import vn.vnpay.tvan.apps.reconciliation.service.EndAuthorizationProcessService;
import vn.vnpay.tvan.libs.common.constant.Constant;
import vn.vnpay.tvan.libs.common.model.Event;

import java.util.ArrayList;
import java.util.List;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Slf4j
@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.end-authorize-sync-message", havingValue = "true")
public class EndAuthorizeSyncMessageJob {

    private final ConcurrentKafkaListenerContainerFactory<String, Event<Object>> endAuthorizationProcessRequestConsumerFactory;

    private final AuthorizationSessionService authorizationSessionService;

    private final EndAuthorizationProcessService endAuthorizationProcessService;

    private final CacheService cacheService;

    public EndAuthorizeSyncMessageJob(@Qualifier("endAuthorizationProcessRequestListenerContainerFactory")
                                      ConcurrentKafkaListenerContainerFactory<String, Event<Object>> endAuthorizationProcessRequestConsumerFactory,
                                      AuthorizationSessionService authorizationSessionService,
                                      EndAuthorizationProcessService endAuthorizationProcessService,
                                      CacheService cacheService) {
        this.endAuthorizationProcessRequestConsumerFactory = endAuthorizationProcessRequestConsumerFactory;
        this.authorizationSessionService = authorizationSessionService;
        this.endAuthorizationProcessService = endAuthorizationProcessService;
        this.cacheService = cacheService;
    }



    @Scheduled(cron = "${scheduler.job.cron.end-authorize-sync-message}")
    public void process() {
        log.info("End Authorize Sync Message Job");
        if (Boolean.TRUE.equals(cacheService.isAuthorized())) {
            log.info("Cannot run Authorize Sync Message Job when authorization session is ON");
        } else {
            try {
                String[] topics = authorizationSessionService.getListTopicsToSyncMessage().toArray(new String[0]);
                if (topics.length > 0) {
                    KafkaListenerEndpoint endpoint = getEndPoint(topics);
                    var container = endAuthorizationProcessRequestConsumerFactory.createListenerContainer(endpoint);
                    container.start();
                } else {
                    log.info("No Authorize Session To Process");
                }
            } catch (Exception ex) {
                log.error("Failed to process End Authorize Sync Message Job", ex);
            }
        }

    }

    private KafkaListenerEndpoint getEndPoint(String[] topics) throws NoSuchMethodException {
        var listenerEndpoint = new MethodKafkaListenerEndpoint<String, Event<Object>>();
        listenerEndpoint.setMessageHandlerMethodFactory(new DefaultMessageHandlerMethodFactory());
        listenerEndpoint.setBean(this);
        listenerEndpoint.setMethod(getClass().getMethod("onMessage", ConsumerRecord.class, Acknowledgment.class));
        listenerEndpoint.setAutoStartup(true);

        ArrayList<TopicPartitionOffset> offsets = new ArrayList<>();

        for (String topic : topics) {
            TopicPartitionOffset offset = new TopicPartitionOffset(topic, 0, TopicPartitionOffset.SeekPosition.BEGINNING);
            offsets.add(offset);
        }

        TopicPartitionOffset[] arrayOffset = offsets.toArray(new TopicPartitionOffset[0]);
        listenerEndpoint.setTopicPartitions(arrayOffset);

        return listenerEndpoint;
    }

    @EventListener
    public void idleEventHandler(ListenerContainerIdleEvent event) {
        MDC.put(TRACE_ID, String.valueOf(System.nanoTime()));
        String listenerId = event.getListenerId();
        log.info("Listener {} idle for time : {}ms", listenerId, event.getIdleTime());
        try {
            MessageListenerContainer container = event.getContainer(ConcurrentMessageListenerContainer.class);
            var partitions = container.getAssignedPartitions();
            List<String> topics = new ArrayList<>();

            if (!CollectionUtils.isEmpty(partitions)) {
                for (var partition : partitions) {
                    String topic = partition.topic();
                    topics.add(topic);
                    log.info("Authorization Topic : {}", topic);
                }
            }

            //Save các sesion với status = 1 (sẵn sàng để gửi 902)
            if (!topics.isEmpty()) {
                for (String topic : topics){
                    log.info("Save value for topic: {}", topic);
                    String sessionId = topic.replace(Constant.AUTHORIZATION_SESSION.AUTHORIZATION, "");
                    authorizationSessionService.saveValueInAuthorizationSession(sessionId);
                }
            }

            //Start send 902 message
            authorizationSessionService.sendReconciliation();

            container.stop();
        } catch (Exception ex) {
            log.error("Error when process send 902 message", ex);
        }
    }

    public void onMessage(ConsumerRecord<String, Event<Object>> record, Acknowledgment ack) {
        log.info("Start process send Sync Message to TCT");
        MDC.put(TRACE_ID, String.valueOf(System.nanoTime()));
        Event<Object> event = record.value();
        String topic = record.topic();
        try {
            endAuthorizationProcessService.sendMessageToTCT(event, topic);
            Thread.sleep(100);
        } catch (Exception ex) {
            log.error("Error while send Sync message to TCT", ex);
        } finally {
            ack.acknowledge();
            MDC.clear();
        }
    }
}
