package vn.vnpay.tvan.apps.reconciliation.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.tvan.apps.reconciliation.repository.CallbackBatchRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.CallbackTransactionRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.EventStoreRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.InvoiceAppRepository;
import vn.vnpay.tvan.apps.reconciliation.service.CallbackService;
import vn.vnpay.tvan.apps.reconciliation.service.EventService;
import vn.vnpay.tvan.apps.reconciliation.service.StorageService;
import vn.vnpay.tvan.libs.common.constant.Constant;
import vn.vnpay.tvan.libs.common.constant.EventType;
import vn.vnpay.tvan.libs.common.constant.ResponseCode;
import vn.vnpay.tvan.libs.common.enumeration.CallbackBatchStatus;
import vn.vnpay.tvan.libs.common.enumeration.CallbackTransactionStatus;
import vn.vnpay.tvan.libs.common.exception.CallBackEInvoiceException;
import vn.vnpay.tvan.libs.common.model.Event;
import vn.vnpay.tvan.libs.common.model.Request;
import vn.vnpay.tvan.libs.common.model.Response;
import vn.vnpay.tvan.libs.common.model.entity.CallbackBatch;
import vn.vnpay.tvan.libs.common.model.entity.CallbackTransaction;
import vn.vnpay.tvan.libs.common.model.entity.EventStore;
import vn.vnpay.tvan.libs.common.model.entity.InvoiceApp;
import vn.vnpay.tvan.libs.common.util.StringUtil;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static vn.vnpay.share.message.constant.MessageType._999_ThongDiepPhanHoiKyThuat;
import static vn.vnpay.share.message.util.MessageUtil.isNullOrEmpty;
import static vn.vnpay.tvan.libs.common.constant.Constant.REDIS.TOTAL_FAIL;
import static vn.vnpay.tvan.libs.common.constant.Constant.REDIS.TOTAL_SUCCESS;
import static vn.vnpay.tvan.libs.common.enumeration.CallbackTransactionStatus.SEND_FAILED;
import static vn.vnpay.tvan.libs.common.enumeration.CallbackTransactionStatus.SEND_SUCCESS;

@Slf4j
@Service
//@ConditionalOnProperty(value = "scheduler.job.enable.tool-callback", havingValue = "true")
public class CallbackServiceImpl implements CallbackService {
    private final ObjectMapper jsonMapper;
    private final EventService eventService;
    private final InvoiceAppRepository invoiceAppRepository;
    private final OkHttpClient okHttpClient;
    private final CallbackTransactionRepository callbackTransactionRepository;
    private final CallbackBatchRepository callbackBatchRepository;
    private final EventStoreRepository eventStoreRepository;
    private final StorageService storageService;
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private final CacheServiceImpl cacheServiceImpl;
    private static final ExecutorService executor = Executors.newCachedThreadPool();

    public CallbackServiceImpl(@Qualifier("jsonMapper") ObjectMapper jsonMapper,
                               EventService eventService,
                               InvoiceAppRepository invoiceAppRepository,
                               OkHttpClient okHttpClient,
                               CallbackTransactionRepository callbackTransactionRepository,
                               CallbackBatchRepository callbackBatchRepository,
                               EventStoreRepository eventStoreRepository,
                               StorageService storageService, CacheServiceImpl cacheServiceImpl) {
        this.jsonMapper = jsonMapper;
        this.eventService = eventService;
        this.invoiceAppRepository = invoiceAppRepository;
        this.okHttpClient = okHttpClient;
        this.callbackTransactionRepository = callbackTransactionRepository;
        this.callbackBatchRepository = callbackBatchRepository;
        this.eventStoreRepository = eventStoreRepository;
        this.storageService = storageService;
        this.cacheServiceImpl = cacheServiceImpl;
    }

    @Override
    public void prepareReCallbackData() {
        log.info("Starting to prepare callback data");
        Optional<CallbackBatch> callbackBatchOpt =
                callbackBatchRepository.findFirstByStatusOrderByCreatedAt(CallbackBatchStatus.IN_PROGRESS.value());

        if (callbackBatchOpt.isPresent()) {
            CallbackBatch callbackBatch = callbackBatchOpt.get();
            log.info("Processing for callback batch : {}", callbackBatch);

            //Query EventStore then insert to CallbackTransaction
            Long batchId = callbackBatch.getId();
            String appId = callbackBatch.getAppId();
            Instant fromDate = callbackBatch.getFromDate();
            Instant toDate = callbackBatch.getToDate();
            Instant toDatePlus7Days = toDate.plus(7, ChronoUnit.DAYS);

            InvoiceApp invoiceApp = invoiceAppRepository.findInvoiceAppByAppId(appId).orElseThrow();
            List<EventStore> eventStores = eventStoreRepository
                    .findByListCanCallbackByAppIdAndCreatedAt(appId, fromDate, toDate, toDatePlus7Days);

            if (!eventStores.isEmpty()) {
                List<CallbackTransaction> callbackTransactions = eventStores.stream()
                        .map(e -> buildCallbackTransaction(invoiceApp, e, batchId))
                        .toList();

                log.info("Fill total sent for callbackBatch");
                callbackBatch.setTotalSent(callbackTransactions.size());
                callbackBatch.setTotalDeviation(0);
                callbackBatch.setTotalReceived(0);
                callbackBatch.setStatus(CallbackBatchStatus.PREPARED.value());

                log.info("Saving {} callback transaction", callbackTransactions.size());
                callbackTransactionRepository.saveAll(callbackTransactions);
            } else {
                callbackBatch.setStatus(CallbackBatchStatus.DONE.value());
            }
            callbackBatchRepository.save(callbackBatch);
        }
    }

    @Override
    public void callbackToInvoice() {
        List<CallbackTransaction> callbackTransactions = callbackTransactionRepository.findListCallbacks();

        if (callbackTransactions.isEmpty()) {
            log.warn("Callback list are empty!");
            return;
        }
        log.info("Total Callback Transaction will send at this job = " + callbackTransactions.size());

        for (var transaction : callbackTransactions) {
            handleCallback(transaction);
        }
        log.info("Finished handling callback!");
        updateCallbackBatchRequest(callbackTransactions);
    }

    private void updateCallbackBatchRequest(List<CallbackTransaction> callbackTransactions) {
        log.info("Started updating callback batch requests");

        List<Long> callbackBatchIds = callbackTransactions.stream().map(CallbackTransaction::getBatchId)
                .distinct().toList();

        if (isNullOrEmpty(callbackBatchIds)) {
            log.warn("Nothing to do here");
            return;
        }

        for (Long batchId : callbackBatchIds) {
            List<CallbackTransaction> callbacks = callbackTransactions.stream().filter(
                    c -> batchId.equals(c.getBatchId())
            ).toList();

            List<CallbackTransaction> successCallbacks = callbacks.stream().filter(
                    c -> SEND_SUCCESS.code().equals(c.getStatus())
            ).toList();

            List<CallbackTransaction> failCallbacks = callbacks.stream().filter(
                    c -> SEND_FAILED.code().equals(c.getStatus())
            ).toList();

            CallbackBatch callbackBatch = callbackBatchRepository.findById(batchId).orElse(null);

            if (nonNull(callbackBatch)) {
                int currentReceived = callbackBatch.getTotalReceived() == null ? 0 : callbackBatch.getTotalReceived();
                int currentFailed = callbackBatch.getTotalDeviation() == null ? 0 : callbackBatch.getTotalDeviation();
                Integer totalReceived =  currentReceived + successCallbacks.size();
                Integer totalFailed =  currentFailed + failCallbacks.size();

                log.info("currentReceived = {}, currentFailed = {}, totalReceived = {}, totalFailed = {}",
                        currentReceived, currentFailed, totalReceived, totalFailed);

                callbackBatch.setTotalReceived(totalReceived);
                callbackBatch.setTotalDeviation(totalFailed);
                callbackBatchRepository.save(callbackBatch);
            }
        }
    }

    public CompletableFuture<Response<Object>> asyncCallBack(Request requestMessage, String appId) {
        Optional<InvoiceApp> invoiceAppOptional = invoiceAppRepository.findInvoiceAppByAppId(appId);

        if (invoiceAppOptional.isEmpty()) {
            log.info("Cannot Found Invoice App with ID {}", appId);
            return CompletableFuture
                    .failedFuture(new IllegalArgumentException("Cannot Found Invoice App with ID " + appId));

        }
        InvoiceApp invoiceApp = invoiceAppOptional.get();
        String callBackEndPoint = invoiceApp.getCallbackEndpoint();
        String callBackApiKey = invoiceApp.getApiKey();

        if (isNull(callBackEndPoint)) {
            log.info("CallbackUrl is not exist: {}", appId);
            return CompletableFuture.completedFuture(
                    Response.builder().code(ResponseCode.SUCCESS.code()).message("CallbackUrl is not exist").build()
            );
        }

        String jsonString;
        try {
            jsonString = jsonMapper.writeValueAsString(requestMessage);
        } catch (JsonProcessingException e) {
            log.error("Cannot parse json data response to E-Invoice ", e);
            return CompletableFuture
                    .failedFuture(new IllegalArgumentException("Cannot parse json data response to E-Invoice"));
        }
        RequestBody body = RequestBody.create(jsonString, JSON);

        okhttp3.Request request = new okhttp3.Request.Builder()
                .url(callBackEndPoint)
                .addHeader("X-API-KEY", callBackApiKey)
                .post(body)
                .build();

        log.info("Start callback to AppId {} with URL : {}", appId, callBackEndPoint);

        CompletableFuture<Response<Object>> future = new CompletableFuture<>();
        okHttpClient.newCall(request).enqueue(createCallBack(future));
        return future;
    }

    private Callback createCallBack(CompletableFuture<Response<Object>> future) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        long startTime = System.currentTimeMillis();
        return new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                try {
                    MDC.setContextMap(contextMap);
                    log.info("callback_to_einvoice{status=fail, process_time={}ms, url={}, error={}}",
                            System.currentTimeMillis() - startTime, call.request().url(), e.getMessage());
                    future.completeExceptionally(e);
                } finally {
                    MDC.clear();
                }

            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull okhttp3.Response response) throws IOException {

                try {
                    MDC.setContextMap(contextMap);
                    log.info("callback_to_einvoice{status=success, process_time={}ms, url={}, code={}}",
                            System.currentTimeMillis() - startTime, call.request().url(), response.code());

                    if (!response.isSuccessful()) {
                        log.info("Response is not successful, status {}, message {}", response.code(), response.message());
                        future.complete(failWithMessage("Response code : " + response.code()
                                + " | Response message : " + response.message()));
                        return;
                    }
                    var body = response.body();

                    if (isNull(body)) {
                        log.info("Response body is null");
                        future.complete(failWithMessage("Response body is null"));
                        return;
                    }

                    final String rawResponse = body.string();
                    log.info("Raw response: " + rawResponse);

                    if (rawResponse.isEmpty()) {
                        log.info("Response body is empty");
                        future.complete(failWithMessage("Response body is empty"));
                        return;
                    }
                    TypeReference<Response<Object>> responseTypeReference = new TypeReference<>() {
                    };
                    Response<Object> res = jsonMapper.readValue(rawResponse, responseTypeReference);
                    future.complete(res);
                } finally {
                    MDC.clear();
                    response.close();
                }
            }
        };
    }

    private Response<Object> failWithMessage(String message) {
        return Response.builder()
                .code(ResponseCode.ERROR.code())
                .message(message)
                .build();
    }

    private void onCallBackFail(EventStore eventStore, MessageType messageType, CallbackTransaction transaction, Throwable ex) {
        Response<String> response = Response.<String>builder()
                .code(ResponseCode.ERROR.code())
                .message(ex.getMessage()).
                build();
        eventService.push(Event.builder()
                .createdAt(Instant.now())
                .initAt(eventStore.getInitAt())
                .appId(transaction.getAppId())
                .taxCode(eventStore.getTaxCode())
                .transactionId(eventStore.getTransactionId())
                .messageType(_999_ThongDiepPhanHoiKyThuat)
                .refMessageType(messageType)
                .type(EventType.RECEIVED_EINVOICE_ACK)
                .data(response)
                .build());

        updateCallbackTransaction(transaction, SEND_FAILED);
    }

    private void onCallBackSuccess(Response<Object> response, MessageType messageType, EventStore eventStore,
                                   CallbackTransaction transaction) {
        log.info("Response from EInvoice : {}", response);
        eventService.push(Event.builder()
                .createdAt(Instant.now())
                .initAt(eventStore.getInitAt())
                .appId(transaction.getAppId())
                .taxCode(eventStore.getTaxCode())
                .transactionId(eventStore.getTransactionId())
                .messageType(_999_ThongDiepPhanHoiKyThuat)
                .refMessageType(messageType)
                .type(EventType.RECEIVED_EINVOICE_ACK)
                .data(response)
                .build());

        updateCallbackTransaction(transaction, SEND_SUCCESS);
        log.info("Send response to e-invoice end");
    }

    private void handleCallback(CallbackTransaction callbackTransaction) {
        log.info("Started handling callback");

        try {
            EventType eventType = EventType.RESPONSE_TO_EINVOICE_HAS_BEEN_SENT;
            EventStore eventStore = EventStore.builder().initAt(callbackTransaction.getCreatedAt())
                    .taxCode(callbackTransaction.getTaxCode())
                    .messageType(callbackTransaction.getMessageType())
                    .transactionId(callbackTransaction.getTransactionId())
                    .storage(callbackTransaction.getStorage())
                    .storagePath(callbackTransaction.getStoragePath())
                    .encryptKey(callbackTransaction.getEncryptKey())
                    .type(eventType.index())
                    .build();

            String responseContent = storageService.getFileFromStorage(eventStore);

            if (nonNull(responseContent)) {
                Request request = buildRequestFromResponseContent(responseContent);

                log.info("Begin send result to e-invoice with transactionId: {}", callbackTransaction.getTransactionId());
                MessageType messageType = MessageType.of(callbackTransaction.getMessageType());

                CompletableFuture<Response<Object>> callbackFuture = asyncCallBack(request, callbackTransaction.getAppId());

                eventService.push(Event.builder()
                        .createdAt(Instant.now())
                        .initAt(callbackTransaction.getCreatedAt())
                        .appId(callbackTransaction.getAppId())
                        .taxCode(callbackTransaction.getTaxCode())
                        .transactionId(callbackTransaction.getTransactionId())
                        .messageType(messageType)
                        .type(EventType.RESPONSE_TO_EINVOICE_HAS_BEEN_SENT)
                        .data(request)
                        .build());

                final var contextMap = MDC.getCopyOfContextMap();

                callbackFuture.whenCompleteAsync((response, ex) -> {
                    try {
                        MDC.setContextMap(contextMap);

                        if (nonNull(ex)) {
                            log.error("Error when send result to e-invoice", ex);
                            onCallBackFail(eventStore, messageType, callbackTransaction, ex);
                        } else {
                            log.info("Receive response from invoice app");

                            if (response.isSuccess()) {
                                log.info("Send response to invoice app Successfully");
                                onCallBackSuccess(response, messageType, eventStore, callbackTransaction);
                            } else {
                                log.info("Send response to invoice app Failed");
                                onCallBackFail(eventStore, messageType, callbackTransaction, ex);
                            }
                        }
                    } finally {
                        MDC.clear();
                        log.info("FUTURE COMPLETED!");
                    }
                }, executor); // Sử dụng executor để chạy bất đồng bộ
                log.info("Result to e-invoice has been sent");
            } else {
                log.warn("Cannot found response content for transactionId = {}", callbackTransaction.getTransactionId());
                updateCallbackTransaction(callbackTransaction, SEND_FAILED);
            }
        } catch (Exception e) {
            log.error("Error when callback to e-invoice transactionId = {}, exception is",
                    callbackTransaction.getTransactionId(), e);
            updateCallbackTransaction(callbackTransaction, SEND_FAILED);
        }
    }

    private CallbackTransaction buildCallbackTransaction(InvoiceApp invoiceApp, EventStore eventStore, Long batchId) {
        return CallbackTransaction.builder()
                .taxCode(eventStore.getTaxCode())
                .callbackUrl(invoiceApp.getCallbackEndpoint())
                .appId(invoiceApp.getAppId())
                .appName(invoiceApp.getName())
                .transactionId(eventStore.getTransactionId())
                .messageId(eventStore.getMessageId())
                .messageType(eventStore.getMessageType())
                .transactionCreatedAt(eventStore.getInitAt())
                .eventStoreCreatedAt(eventStore.getCreatedAt())
                .createdAt(Instant.now())
                .status(CallbackTransactionStatus.NOT_SEND.code())
                .storage(eventStore.getStorage())
                .storagePath(eventStore.getStoragePath())
                .encryptKey(eventStore.getEncryptKey())
                .batchId(batchId)
                .build();
    }

    private Request buildRequestFromResponseContent(String responseContent) {
        log.info("Starting to build request from response content.");
        JsonNode jsonNode;
        JsonNode data;
        JsonNode soLuong;
        JsonNode mstNnt;
        try {
            jsonNode = jsonMapper.readTree(responseContent);
            data = jsonNode.get("data");
            soLuong = jsonNode.get("soLuong");
            mstNnt = jsonNode.get("mstNnt");
        } catch (JsonProcessingException e) {
            log.error("Error when read json", e);
            throw new RuntimeException(e);
        }

        log.info("Read json successfully!");
        log.info("Started to get data sLg from json node");
        String slgString = getDataFromJsonNode(soLuong);
        Integer sLg;
        try {
            sLg = Integer.valueOf(slgString);
        } catch (Exception e) {
            sLg = null;
        }

        log.info("Started to get data mst from json node");
        String mst = getDataFromJsonNode(mstNnt);

        log.info("Finished building request from response content.");
        return Request.builder().mstNnt(mst)
                .data(data.asText())
                .soLuong(sLg)
                .build();
    }

    private String getDataFromJsonNode(JsonNode jsonNode){
        try{
            return jsonNode.asText();
        }catch (NullPointerException exception){
            log.info("Json node is null");
            return null;
        }
    }

    private void updateCallbackTransaction(CallbackTransaction transaction, CallbackTransactionStatus status) {
        transaction.setCallbackAt(Instant.now());
        transaction.setStatus(status.code());
        callbackTransactionRepository.save(transaction);
    }
}
