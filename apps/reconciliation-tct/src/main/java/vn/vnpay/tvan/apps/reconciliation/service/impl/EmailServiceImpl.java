package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.mail.javamail.MimeMessagePreparator;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;
import org.thymeleaf.spring5.templateresolver.SpringResourceTemplateResolver;
import org.thymeleaf.templatemode.TemplateMode;
import vn.vnpay.tvan.apps.reconciliation.constant.Constant;
import vn.vnpay.tvan.apps.reconciliation.constant.TemplateEmail;
import vn.vnpay.tvan.apps.reconciliation.model.EmailConfig;
import vn.vnpay.tvan.apps.reconciliation.repository.EmailConfigRepository;
import vn.vnpay.tvan.apps.reconciliation.service.EmailService;

import javax.activation.DataSource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;
import java.util.Properties;

import static java.util.Objects.nonNull;


@Slf4j
@Service
public class EmailServiceImpl implements EmailService {

    private final EmailConfigRepository emailConfigRepository;

    private final ApplicationContext applicationContext;

    public EmailServiceImpl(EmailConfigRepository emailConfigRepository, ApplicationContext applicationContext) {
        this.emailConfigRepository = emailConfigRepository;
        this.applicationContext = applicationContext;
    }

    @Value("${mail.host}")
    private String host;

    @Value("${mail.port}")
    private int port;

    @Value("${mail.username}")
    private String username;

    @Value("${mail.passwordEncrypt}")
    private String passwordEncrypt;

    @Value("${mail.from}")
    private String mailFrom;


    /**
     * Get Java mail sender
     * @return JavaMailSenderImpl
     */
    private JavaMailSenderImpl getJavaMailSender(){
        byte[] byteArray = Base64.decodeBase64(passwordEncrypt.getBytes());
        String password = new String(byteArray);

        log.info("Send email start");
        JavaMailSenderImpl mailSenderConfig = new JavaMailSenderImpl();
        mailSenderConfig.setUsername(username);
        mailSenderConfig.setPassword(password);
        mailSenderConfig.setHost(host);
        mailSenderConfig.setPort(port);
        Properties props = mailSenderConfig.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.debug", "true");

        return mailSenderConfig;
    }

    @Override
    public boolean sendMailWithAttachment(String fileName, DataSource dataSource) {
        Optional<EmailConfig> emailConfigOptional
                = emailConfigRepository.findByName(Constant.CONFIG_KEY.RECONCILIATION_EMAIL_CONFIG);

        if (emailConfigOptional.isEmpty()) {
            log.error("Email config not found");
            return false;
        }

        EmailConfig emailConfig = emailConfigOptional.get();

        JavaMailSenderImpl mailSender = getJavaMailSender();

        MimeMessagePreparator preparator = mimeMessage -> {

            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            if (isNotBlank(emailConfig.getEmailTo())) {
                message.setTo(emailConfig.getEmailTo().split(","));
            }
            if (isNotBlank(emailConfig.getEmailCc())) {
                message.setCc(emailConfig.getEmailCc().split(","));
            }
            message.setSubject(emailConfig.getSubject());
            message.setText(emailConfig.getBody());
            message.addAttachment(fileName, dataSource);
            message.setFrom(mailFrom);
        };

        try {
            mailSender.send(preparator);
            log.info("Send email finished");
            return true;
        } catch (MailException ex) {
            log.error("Error when send email", ex);
            return false;
        }
    }

    private boolean isNotBlank(String s) {
        return nonNull(s) && s.trim().length() > 0;
    }


    @Override
    public void sendHtmlMail(String to, TemplateEmail templateEmail, Context context) throws MessagingException {
        SpringTemplateEngine templateEngine = getSpringTemplateEngine();
        String body = templateEngine.process(templateEmail.contentPath(), context);

        JavaMailSenderImpl javaMailSender = getJavaMailSender();

        MimeMessage mail = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mail, true, StandardCharsets.UTF_8.name());
        helper.setTo(to);
        helper.setFrom(mailFrom);
        helper.setSubject(templateEmail.subject());
        helper.setText(body, true);

        javaMailSender.send(mail);
    }
    @Override
    public void sendHtmlMailToGroup(String[] to, TemplateEmail templateEmail,String subject, Context context) throws MessagingException {
        SpringTemplateEngine templateEngine = getSpringTemplateEngine();
        String body = templateEngine.process(templateEmail.contentPath(), context);

        JavaMailSenderImpl javaMailSender = getJavaMailSender();

        MimeMessage mail = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mail, true, StandardCharsets.UTF_8.name());
        helper.setTo(to);
        helper.setFrom(mailFrom);
        helper.setSubject(subject);
        helper.setText(body, true);

        javaMailSender.send(mail);
    }

    @Override
    public void sendHtmlMailToGroup(String[] to, TemplateEmail templateEmail, Context context) throws MessagingException {
        SpringTemplateEngine templateEngine = getSpringTemplateEngine();
        String body = templateEngine.process(templateEmail.contentPath(), context);

        JavaMailSenderImpl javaMailSender = getJavaMailSender();

        MimeMessage mail = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mail, true, StandardCharsets.UTF_8.name());
        helper.setTo(to);
        helper.setFrom(mailFrom);
        helper.setSubject(templateEmail.subject());
        helper.setText(body, true);

        javaMailSender.send(mail);
    }

    /**
     * Get SpringTemplateEngine
     * @return SpringTemplateEngine
     */
    public SpringTemplateEngine getSpringTemplateEngine() {
        SpringTemplateEngine templateEngine = new SpringTemplateEngine();
        templateEngine.addTemplateResolver(htmlTemplateResolver());
        return templateEngine;
    }

    public SpringResourceTemplateResolver htmlTemplateResolver(){
        SpringResourceTemplateResolver emailTemplateResolver = new SpringResourceTemplateResolver();
        emailTemplateResolver.setApplicationContext(applicationContext);
        emailTemplateResolver.setPrefix("classpath:/template/");
        emailTemplateResolver.setSuffix(".html");
        emailTemplateResolver.setTemplateMode(TemplateMode.HTML);
        emailTemplateResolver.setCharacterEncoding(StandardCharsets.UTF_8.name());
        emailTemplateResolver.setCacheable(false);
        return emailTemplateResolver;
    }

}
