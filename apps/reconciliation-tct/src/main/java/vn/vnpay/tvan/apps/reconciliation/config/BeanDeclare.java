package vn.vnpay.tvan.apps.reconciliation.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import io.minio.MinioClient;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class BeanDeclare {

    @Value(value = "${app.restTemplateTimeoutInMs:30000}")
    private int restTemplateTimeoutInMs;

    @Bean
    @Qualifier("xmlMapper")
    public XmlMapper xmlMapper() {
        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.registerModule(new JaxbAnnotationModule());
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        xmlMapper.configure(ToXmlGenerator.Feature.WRITE_XML_DECLARATION, true);
        return xmlMapper;
    }

    @Bean
    @Qualifier("jsonMapper")
    @Primary
    public ObjectMapper jsonMapper() {
        return new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Bean
    public MappingJackson2HttpMessageConverter jsonConverter(@Qualifier("jsonMapper") ObjectMapper jsonMapper) {
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        jsonConverter.setObjectMapper(jsonMapper);
        jsonConverter.setSupportedMediaTypes(List.of(MediaType.APPLICATION_JSON));
        return jsonConverter;
    }

    @Bean
    public MappingJackson2HttpMessageConverter xmlConverter(@Qualifier("xmlMapper") XmlMapper xmlMapper) {
        MappingJackson2HttpMessageConverter xmlConverter = new MappingJackson2HttpMessageConverter();
        xmlConverter.setObjectMapper(xmlMapper);
        xmlConverter.setSupportedMediaTypes(List.of(MediaType.APPLICATION_XML));
        return xmlConverter;
    }

    @Bean
    public OkHttpClient okHttpClient(){
        return new OkHttpClient.Builder()
                .connectTimeout(restTemplateTimeoutInMs, TimeUnit.MILLISECONDS)
                .writeTimeout(restTemplateTimeoutInMs, TimeUnit.MILLISECONDS)
                .readTimeout(restTemplateTimeoutInMs, TimeUnit.MILLISECONDS)
                .build();
    }
}
