package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DeleteTopicsResult;
import org.apache.kafka.clients.admin.ListTopicsOptions;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.repository.AuthorizationSessionRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.remove-not-use-sync-topic", havingValue = "true")
public class RemoveNotUseSyncTopicJob {
    private final AdminClient kafkaClient;

    private final AuthorizationSessionRepository authorizationSessionRepository;

    public RemoveNotUseSyncTopicJob(AdminClient kafkaClient,
                                    AuthorizationSessionRepository authorizationSessionRepository) {
        this.kafkaClient = kafkaClient;

        this.authorizationSessionRepository = authorizationSessionRepository;
    }

    @Scheduled(cron = "${scheduler.job.cron.remove-not-use-sync-topic}")
    public void process() {
        log.info("Remove Not Used Sync Topics Job start");
        List<String> notUsedTopics = authorizationSessionRepository.getListTopicNotUsed();
        if (!notUsedTopics.isEmpty()) {
            log.info("List Authorize Sync Topics Is About To Be Deleted :" + notUsedTopics);
            try {
                ListTopicsOptions options = new ListTopicsOptions();
                options.listInternal(true); // includes internal topics such as __consumer_offsets
                ListTopicsResult topics = kafkaClient.listTopics(options);
                Set<String> currentTopicList = topics.names().get();

                List<String> listTopicsNeedToDelete = currentTopicList.stream().filter(notUsedTopics::contains).toList();

                DeleteTopicsResult result = kafkaClient.deleteTopics(listTopicsNeedToDelete);
                result.topicNameValues().forEach((t,k) -> {
                    while (!k.isDone()) {
                        //Just wait
                    }
                    if (k.isDone()) {
                        log.info("Deleted topic : " + t);
                    }
                });
            } catch (Exception ex) {
                log.error("Error while delete Authorize Sync Topic", ex);
            }
        }

    }
}
