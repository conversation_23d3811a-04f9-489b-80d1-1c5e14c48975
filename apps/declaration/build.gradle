group 'vn.vnpay.tvan.apps'
version '2.7.0'

dependencies {
    implementation "org.springframework.cloud:spring-cloud-starter-config"
    implementation "vn.vnpay.share:1450-message:${nd1450MessageVersion}"
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    implementation "de.siegmar:logback-gelf:${grayLogVersion}"
    implementation "org.springframework.boot:spring-boot-starter-data-jpa"
    implementation "commons-io:commons-io:${commonIoVersion}"
    implementation "com.fasterxml.jackson.module:jackson-module-jaxb-annotations"
    implementation "com.fasterxml.jackson.dataformat:jackson-dataformat-xml"
    runtimeOnly "com.oracle.database.jdbc:ojdbc8"
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation "org.mapstruct:mapstruct:${mapstructVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

    //Swagger
    implementation "org.springdoc:springdoc-openapi-ui:${openApiUiVersion}"
    implementation "org.openapitools:jackson-databind-nullable:${jacksonDatabindNullableVersion}"
    implementation "com.fasterxml.jackson.dataformat:jackson-dataformat-xml:${jacksonVersion}"
    implementation "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:${jacksonVersion}"
    implementation "io.swagger.core.v3:swagger-annotations:${swaggerAnnotationVersion}"

    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation project(':libs:security')
    implementation project(':libs:common')

    implementation "com.google.guava:guava:${guavaVersion}"


}

test {
    useJUnitPlatform()
}
task protectJar(type: Exec) {
    dependsOn build

    workingDir "${rootDir}".toString()

    commandLine 'cmd', '/c', 'java', '-jar', 'libs/stringer.jar',
            '-configFile', 'stringer.xml',
            'apps/declaration/build/libs/declaration-' << getVersion() << '.jar',
            'apps/declaration/build/libs/tvan-declaration-protected-'<< getVersion() << '.jar'

    println("Protected Jar file!")
}
