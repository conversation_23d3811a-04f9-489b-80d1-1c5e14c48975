package vn.vnpay.tvan.apps.reconciliation.service.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.tvan.libs.common.model.RequestInfo;
import vn.vnpay.tvan.libs.common.model.entity.RequestInfoEntity;

import java.util.zip.CRC32;

import static java.util.Objects.isNull;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface RequestInfoMapper {

    @Named("crc32")
    static long crc32(String data) {
        if (isNull(data)) return 0;
        CRC32 crc = new CRC32();
        crc.update(data.getBytes());
        return crc.getValue();
    }

    @Named("messageTypeFromCode")
    static MessageType fromCode(Integer code) {
        return MessageType.of(code);
    }

    @Named("messageTypeToCode")
    static Integer toCode(MessageType messageType) {
        if (isNull(messageType)) return null;
        return messageType.code();
    }

    @Mapping(source = "messageType", target = "messageType", qualifiedByName = "messageTypeFromCode")
    RequestInfo fromEntity(RequestInfoEntity entity);

    @Mapping(source = "transactionId", target = "transactionIdHash", qualifiedByName = "crc32")
    @Mapping(source = "messageType", target = "messageType", qualifiedByName = "messageTypeToCode")
    RequestInfoEntity toEntity(RequestInfo entity);
}
