package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import vn.vnpay.tvan.apps.reconciliation.model.EmailConfig;
import vn.vnpay.tvan.apps.reconciliation.repository.EmailConfigRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.EmailHistoryRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.MissingMessageRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.SystemConfigRepository;
import vn.vnpay.tvan.apps.reconciliation.service.EmailService;
import vn.vnpay.tvan.apps.reconciliation.service.MissingMessageWarningService;
import vn.vnpay.tvan.libs.common.constant.EmailHistoryType;
import vn.vnpay.tvan.libs.common.model.entity.EmailHistory;
import vn.vnpay.tvan.libs.common.model.entity.MissingMessage;
import vn.vnpay.tvan.libs.common.model.entity.SystemConfig;

import javax.mail.MessagingException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.time.Instant.now;
import static vn.vnpay.share.message.constant.MessageType._100_ThongDiepGuiToKhai;
import static vn.vnpay.share.message.constant.MessageType._102_ThongDiepThongBaoTiepNhanToKhai;
import static vn.vnpay.share.message.constant.MessageType._103_ThongDiepThongBaoChapNhanToKhai;
import static vn.vnpay.share.message.constant.MessageType._200_ThongDiepGuiCapMa;
import static vn.vnpay.share.message.constant.MessageType._202_ThongDiepThongBaoKetQuaCapMa;
import static vn.vnpay.share.message.constant.MessageType._203_ThongDiepChuyenDuLieuKhongMa;
import static vn.vnpay.share.message.constant.MessageType._204_ThongDiepThongBaoKetQuaKiemTraDuLieu;
import static vn.vnpay.share.message.constant.MessageType._206_ThongDiepGuiCapMaHoaDonTaoTuMayTinhTien;
import static vn.vnpay.share.message.constant.MessageType._300_ThongDiepThongBaoHoaDonSaiSot;
import static vn.vnpay.share.message.constant.MessageType._301_ThongDiepThongBaoTiepNhanXuLyHoaDonSaiSot;
import static vn.vnpay.share.message.constant.MessageType._303_ThongDiepThongBaoHoaDonSaiSotTuMayTinhTien;
import static vn.vnpay.share.message.constant.MessageType._400_ThongDiepChuyenBangTongHopDuLieu;
import static vn.vnpay.share.message.constant.MessageType._999_ThongDiepPhanHoiKyThuat;
import static vn.vnpay.tvan.apps.reconciliation.constant.Constant.CONFIG_KEY.TCT_HAVE_NOT_RESPONSE_YET_EMAIL_CONFIG;
import static vn.vnpay.tvan.apps.reconciliation.constant.Constant.EMAIL_SENDER;
import static vn.vnpay.tvan.apps.reconciliation.constant.TemplateEmail.WARNING_TCT_HAVE_NOT_RESPONSE_YET;
import static vn.vnpay.tvan.libs.common.constant.Constant.SystemConfig.NAME_OF_CONFIG_THAT_TRANSACTIONS_TCT_HAVE_NOT_RESPONSE_YET;
import static vn.vnpay.tvan.libs.common.util.Utils.nonNullOrEmpty;

@Slf4j
@Service
public class MissingMessageWarningServiceImpl implements MissingMessageWarningService {
    private final SystemConfigRepository systemConfigRepository;
    private final MissingMessageRepository missingMessageRepository;
    private final EmailService emailService;
    private final EmailHistoryRepository emailHistoryRepository;
    private final EmailConfigRepository emailConfigRepository;

    public MissingMessageWarningServiceImpl(SystemConfigRepository systemConfigRepository,
                                            MissingMessageRepository missingMessageRepository,
                                            EmailService emailService,
                                            EmailHistoryRepository emailHistoryRepository,
                                            EmailConfigRepository emailConfigRepository) {
        this.systemConfigRepository = systemConfigRepository;
        this.missingMessageRepository = missingMessageRepository;
        this.emailService = emailService;
        this.emailHistoryRepository = emailHistoryRepository;
        this.emailConfigRepository = emailConfigRepository;
    }

    @Override
    public void warningTransactionThatTctHaveNotResponseYet() {
        /*Lấy thông tin cấu hình cảnh báo TCT chưa phản hồi*/
        SystemConfig systemConfig = systemConfigRepository
                .findByName(NAME_OF_CONFIG_THAT_TRANSACTIONS_TCT_HAVE_NOT_RESPONSE_YET);

        /*Các mã thông điệp cần cảnh báo*/
        List<Integer> messageNeedToWarning = List.of(_100_ThongDiepGuiToKhai.code(), _200_ThongDiepGuiCapMa.code(),
                _203_ThongDiepChuyenDuLieuKhongMa.code(), _206_ThongDiepGuiCapMaHoaDonTaoTuMayTinhTien.code(),
                _300_ThongDiepThongBaoHoaDonSaiSot.code(), _303_ThongDiepThongBaoHoaDonSaiSotTuMayTinhTien.code(),
                _400_ThongDiepChuyenBangTongHopDuLieu.code());

        /*Lấy các giao dịch đăng ký/thay đổi thông tin sử dụng hóa đơn điện tử theo ngày được config T - n*/
        List<MissingMessage> missingMessages = missingMessageRepository
                .findAllMissingMessageInConfiguredDate(Integer.parseInt(systemConfig.getValue()), messageNeedToWarning);

        log.info("Transactions need to warning size = " + missingMessages.size());
        if(nonNullOrEmpty(missingMessages)){
            emailWarningTCTHaveNotResponseYet(missingMessages);
        }
    }

    private void emailWarningTCTHaveNotResponseYet(List<MissingMessage> transactionsToEmailWarning) {
        log.info("Started getting email configured to warning");
        Optional<EmailConfig> emailConfig = emailConfigRepository
                .findByName(TCT_HAVE_NOT_RESPONSE_YET_EMAIL_CONFIG);

        if (emailConfig.isPresent()) {
            String emailsConfigured =  emailConfig.get().getEmailTo();
            log.info("Email configured to warning : "+ emailsConfigured);
            List<String> emails = Arrays.asList(emailsConfigured.split(","));
            sendMailWarning(emails,transactionsToEmailWarning);
        }

    }

    private List<Integer> getMessageTypeTctResponseByMessageType(Integer messageType) {
        log.info("Started get messages TCT gonna response by message type = " + messageType);

        if (Objects.equals(messageType, _100_ThongDiepGuiToKhai.code())) {
            return List.of(_999_ThongDiepPhanHoiKyThuat.code(),
                    _102_ThongDiepThongBaoTiepNhanToKhai.code(), _103_ThongDiepThongBaoChapNhanToKhai.code());
        }
        if (Objects.equals(messageType, _200_ThongDiepGuiCapMa.code())
                || Objects.equals(messageType, _206_ThongDiepGuiCapMaHoaDonTaoTuMayTinhTien.code())) {
            return List.of(_999_ThongDiepPhanHoiKyThuat.code(),
                    _202_ThongDiepThongBaoKetQuaCapMa.code(), _204_ThongDiepThongBaoKetQuaKiemTraDuLieu.code());
        }

        if (Objects.equals(messageType, _203_ThongDiepChuyenDuLieuKhongMa.code())) {
            return List.of(_999_ThongDiepPhanHoiKyThuat.code(), _204_ThongDiepThongBaoKetQuaKiemTraDuLieu.code());
        }

        if (Objects.equals(messageType, _300_ThongDiepThongBaoHoaDonSaiSot.code())
                || Objects.equals(messageType, _303_ThongDiepThongBaoHoaDonSaiSotTuMayTinhTien.code())) {
            return List.of(_999_ThongDiepPhanHoiKyThuat.code(), _204_ThongDiepThongBaoKetQuaKiemTraDuLieu.code(),
                    _301_ThongDiepThongBaoTiepNhanXuLyHoaDonSaiSot.code());
        }

        if (Objects.equals(messageType, _400_ThongDiepChuyenBangTongHopDuLieu.code())) {
            return List.of(_999_ThongDiepPhanHoiKyThuat.code(), _204_ThongDiepThongBaoKetQuaKiemTraDuLieu.code());
        }

        return Collections.emptyList();
    }

    private void sendMailWarning(List<String> emailList, List<MissingMessage> missingMessages) {
        String[] emails = emailList.toArray(new String[0]);
        Context context = new Context();

        try {
            log.info("Started to sending email warning!");
            Instant sendTime = now();
            log.info("Send mail time = " + sendTime);
            emailService.sendHtmlMailToGroup(emails, WARNING_TCT_HAVE_NOT_RESPONSE_YET, context);

            saveEmailHistory(sendTime, missingMessages);
        } catch (MessagingException e) {
            log.error("Send email error: ", e);
        }
    }

    private void saveEmailHistory(Instant sendTime, List<MissingMessage> missingMessages) {
        log.info("Started enriching email history");
        List<EmailHistory> historyList = new ArrayList<>();
        missingMessages.forEach(x -> {
            EmailHistory history = new EmailHistory();
            history.setType(EmailHistoryType.WARNING_TCT_HAVE_NOT_RESPONSE_YET);
            history.setSentBy(EMAIL_SENDER);
            history.setSentAt(sendTime);
            history.setDescription("Gửi email cảnh báo TCT chưa phản hồi");
            history.setReferenceCode(x.getId());
            historyList.add(history);
        });

        emailHistoryRepository.saveAll(historyList);
    }
}
