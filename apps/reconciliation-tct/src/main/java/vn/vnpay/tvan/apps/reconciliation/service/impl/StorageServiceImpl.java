package vn.vnpay.tvan.apps.reconciliation.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.tvan.apps.reconciliation.service.StorageService;
import vn.vnpay.tvan.libs.common.constant.EventType;
import vn.vnpay.tvan.libs.common.exception.StorageException;
import vn.vnpay.tvan.libs.common.model.entity.EventStore;
import vn.vnpay.tvan.libs.common.util.AESUtil;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;
import static java.util.Objects.requireNonNull;
import static vn.vnpay.tvan.libs.common.constant.Constant.STORAGE.MINIO;
import static vn.vnpay.tvan.libs.common.constant.Constant.STORAGE.VNPAY_CLOUD;
import static vn.vnpay.tvan.libs.common.util.Utils.generateMinioPath;

@Slf4j
@Service
@RequiredArgsConstructor
public class StorageServiceImpl implements StorageService {

    private final MinioClient minioClient;

    private final AmazonS3 amazonS3;

    @Value("${minio.bucket}")
    private String minioBucket;

    @Value("${s3-cloud.bucket}")
    private String s3cloudBucket;

    @Value("${storage.master-key}")
    private String masterKey;

    @Override
    public String getFileFromStorage(EventStore event) throws Exception {
        log.info("Starting to get file from storage");

        if (nonNull(event.getEncryptKey()) && nonNull(event.getStoragePath())) {
            String fileName = event.getStoragePath();
            log.info("Request to get Obj from Storage with path = {}", fileName);

            String encryptKeyInBase64 = event.getEncryptKey();
            byte[] encryptKeyInBytes = Base64.getDecoder().decode(encryptKeyInBase64);
            byte[] decryptInBytes = AESUtil.decrypt(encryptKeyInBytes, masterKey, masterKey.length() * 8);
            String finalKey = new String(decryptInBytes, StandardCharsets.UTF_8);

            if (VNPAY_CLOUD.equals(event.getStorage())) {
                return getFileFromS3Cloud(finalKey, fileName);
            }
            if (MINIO.equals(event.getStorage())) {
                return getFileFromMinio(finalKey, fileName);
            }
        }
        else {
            log.info("Encrypt key and storage path is null, started getting message content from minio.");
            return getMessageContentFromMinio(event);
        }

        log.info("Finished getting file from storage");
        return null;
    }

    private String getFileFromS3Cloud(String finalKey, String fileName) throws Exception {
        log.info("Started getting file from s3 cloud");
        GetObjectRequest getObjectRequest = new GetObjectRequest(s3cloudBucket, fileName);
        S3Object s3Object = amazonS3.getObject(getObjectRequest);

        try (S3ObjectInputStream is = s3Object.getObjectContent()) {
            byte[] encryptBytes = IOUtils.toByteArray(is);
            byte[] decryptBytes = AESUtil.decrypt(encryptBytes, finalKey, finalKey.length() * 8);
            log.info("Finished getting file from s3 cloud.");
            return new String(decryptBytes, StandardCharsets.UTF_8);
        }
    }

    private String getFileFromMinio(String finalKey, String fileName) throws Exception {
        log.info("Started getting file from minio.");
        GetObjectArgs args = GetObjectArgs.builder()
                .bucket(minioBucket)
                .object(fileName)
                .build();

        try (InputStream is = minioClient.getObject(args)) {
            byte[] encryptBytes = IOUtils.toByteArray(is);
            byte[] decryptBytes = AESUtil.decrypt(encryptBytes, finalKey, finalKey.length() * 8);

            return new String(decryptBytes, StandardCharsets.UTF_8);
        }
    }

    private String getMessageContentFromMinio(EventStore event) {
        String fileName = generateMinioPath(
                event.getInitAt(),
                event.getTaxCode(),
                requireNonNull(EventType.from(event.getType())),
                MessageType.of(event.getMessageType()),
                event.getTransactionId()
        );
        log.info("Event: {}", event);
        log.info("Request to get object in minio: filePath = {}", fileName);
        InputStream inputStream;
        try {
            inputStream = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(minioBucket)
                    .object(fileName)
                    .build()
            );
        } catch (Exception e) {
            log.info("Get object error: filePath : " + fileName);
            log.error(e.toString());
            throw new StorageException("Get object error: filePath : " + fileName);
        }

        return new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8)
        ).lines()
                .collect(Collectors.joining("\n"));

    }
}
