<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="https://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="https://www.liquibase.org/xml/ns/pro"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="https://www.liquibase.org/xml/ns/dbchangelog-ext
                    https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                     https://www.liquibase.org/xml/ns/pro
                     https://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd
                     http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.8.xsd">
    <changeSet author="danpq (generated)" id="1709631699827-1">
        <createTable tableName="CONTRACT">
            <column autoIncrement="true" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="CONTRACT_PK"/>
            </column>
            <column name="CONTRACT_NUMBER" type="VARCHAR2(20 BYTE)"/>
            <column name="CONTRACT_TYPE" type="NUMBER"/>
            <column name="CONTRACT_TYPE_NAME" type="VARCHAR2(200 BYTE)"/>
            <column name="PAYMENT_TYPE" remarks="0 - PREPAID , 1 - POSTPAID" type="NUMBER"/>
            <column name="CUSTOMER_ID" type="NUMBER">
                <constraints nullable="false"/>
            </column>
            <column name="FROM_DATE" type="date"/>
            <column name="TO_DATE" type="date"/>
            <column name="STATUS" remarks="0 - INACTIVE , 1 - ACTIVE , 2 - COMING_SOON" type="NUMBER(3, 0)"/>
            <column name="TOTAL_TRANSMIT_TURN" type="NUMBER"/>
            <column name="TOTAL_USED_TURN" type="NUMBER"/>
            <column name="AGENT_ID" type="NUMBER"/>
            <column name="AGENT_CODE" type="VARCHAR2(100 BYTE)"/>
            <column name="AGENT_TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="CUSTOMER_TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="DESCRIPTION" type="VARCHAR2(200 BYTE)"/>
            <column name="CREATED_AT" type="date"/>
            <column name="CUSTOMER_CODE" type="VARCHAR2(50 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-2">
        <createTable tableName="CUSTOMER">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="CUSTOMER_PK"/>
            </column>
            <column name="NAME" type="VARCHAR2(255 BYTE)"/>
            <column name="ADDRESS" type="VARCHAR2(255 BYTE)"/>
            <column name="CODE" type="VARCHAR2(255 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="CONTACT_POSITION" type="VARCHAR2(255 BYTE)"/>
            <column name="CONTACT_NAME" type="VARCHAR2(255 BYTE)"/>
            <column name="CONTACT_PHONE" type="VARCHAR2(15 BYTE)"/>
            <column name="CONTACT_EMAIL" type="VARCHAR2(255 BYTE)"/>
            <column name="LEGAL_REPRESENTATIVE_NAME" type="VARCHAR2(255 BYTE)"/>
            <column name="LEGAL_REPRESENTATIVE_POSITION" type="VARCHAR2(255 BYTE)"/>
            <column name="STATUS" remarks="0 - INACTIVE, 1 - ACTIVE," type="NUMBER(3, 0)"/>
            <column name="ACTIVE_CONTRACT_ID" type="NUMBER"/>
            <column name="TIER" type="NUMBER(1, 0)"/>
            <column name="PARENT_CUSTOMER_CODE" type="VARCHAR2(50 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-3">
        <createTable tableName="EVENT_STORE">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="EVENT_STORE_PK"/>
            </column>
            <column defaultValueComputed="SYSDATE" name="CREATED_AT" type="TIMESTAMP(6)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)"/>
            <column name="SENDER_ID" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="RECEIVER_ID" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_ID" type="VARCHAR2(46 BYTE)"/>
            <column name="REF_MESSAGE_ID" type="VARCHAR2(46 BYTE)"/>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="REF_MESSAGE_TYPE" type="NUMBER(3, 0)"/>
            <column name="QUANTITY" type="NUMBER(3, 0)"/>
            <column name="STATUS" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="CODE" type="VARCHAR2(50 BYTE)"/>
            <column name="DESCRIPTION" type="NVARCHAR2(1000)"/>
            <column name="IS_AUTHORIZED" type="NUMBER(1, 0)"/>
            <column name="INIT_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-4">
        <createTable tableName="TOTAL_MESSAGE_REPORT">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;SEQ_TOTAL_MESSAGE_REPORT&quot;.&quot;NEXTVAL&quot;" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C008745"/>
            </column>
            <column name="REPORT_DATE" type="date"/>
            <column name="REQUEST_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="RESPONSE_TYPE" type="NUMBER(3, 0)"/>
            <column name="TOTAL" type="NUMBER">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-5">
        <createTable tableName="ABOUT_TO_EXPIRE_CUSTOMER">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ABOUT_TO_EXPIRE_CUSTOMER_PK"/>
            </column>
            <column name="NAME" type="VARCHAR2(255 BYTE)"/>
            <column name="ADDRESS" type="VARCHAR2(100 BYTE)"/>
            <column name="CODE" type="VARCHAR2(100 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="CONTACT_PHONE" type="VARCHAR2(15 BYTE)"/>
            <column name="CONTACT_EMAIL" type="VARCHAR2(200 BYTE)"/>
            <column name="AGENT_TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="CONTRACT_TYPE" type="NUMBER"/>
            <column name="STATUS" remarks="0 - ABOUT_TO_EXPIRE, 1 - RENEWED, 2 - EXPIRED" type="NUMBER(3, 0)"/>
            <column name="NOTIFIED_AT" type="date"/>
            <column name="HAS_BEEN_NOTIFIED" type="NUMBER(1, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-6">
        <createTable tableName="TRANSACTION">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="TRANSACTION_PK"/>
            </column>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE_NAME" type="VARCHAR2(100 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CUSTOMER_ID" type="NUMBER">
                <constraints nullable="false"/>
            </column>
            <column name="CONTRACT_NUMBER" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR2(100 BYTE)"/>
            <column defaultValueComputed="SYSDATE" name="CREATED_AT" type="date"/>
            <column name="MODIFIED_AT" type="date"/>
            <column name="MODIFIED_USER" type="VARCHAR2(50 BYTE)"/>
            <column name="IS_AUTHORIZED" type="NUMBER(1, 0)"/>
            <column name="TRACE_ID" type="VARCHAR2(100 BYTE)"/>
            <column name="QUANTITY" type="NUMBER"/>
            <column name="APP_ID" type="VARCHAR2(15 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-7">
        <createTable tableName="DAILY_INVOICE_MESSAGE_REPORT">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false"/>
            </column>
            <column name="REPORT_DATE" type="date"/>
            <column name="APP_ID" type="VARCHAR2(15 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)"/>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)"/>
            <column defaultValueNumeric="0" name="TOTAL" type="NUMBER">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-8">
        <createTable tableName="DAILY_MESSAGE_REPORT_TCT">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="MESSAGE_REPORT_TCT_PK"/>
            </column>
            <column name="REPORT_DATE" type="date"/>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="RESPONSE_TYPE" type="NUMBER(4, 0)"/>
            <column name="TOTAL" type="NUMBER">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-9">
        <createTable tableName="TAX_CODE_EVENT">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="TAX_CODE_EVENT_PK"/>
            </column>
            <column name="CODE" type="VARCHAR2(46 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="TAX_PAYER_NAME" type="VARCHAR2(100 BYTE)"/>
            <column name="TAX_PAYER_TYPE" type="VARCHAR2(5 BYTE)"/>
            <column name="STATUS" type="VARCHAR2(5 BYTE)"/>
            <column name="REGISTERED_AT" type="date"/>
            <column name="IDENTITY_NUMBER" type="VARCHAR2(12 BYTE)"/>
            <column name="TAX_AUTHORITY_CODE" type="VARCHAR2(10 BYTE)"/>
            <column name="CHAPTER_SUPPLY_CODE" type="VARCHAR2(10 BYTE)"/>
            <column name="CHAPTER_CODE" type="VARCHAR2(10 BYTE)"/>
            <column name="ADDRESS" type="VARCHAR2(100 BYTE)"/>
            <column name="PROVINCE" type="VARCHAR2(50 BYTE)"/>
            <column name="DISTRICT" type="VARCHAR2(50 BYTE)"/>
            <column name="WARD" type="VARCHAR2(50 BYTE)"/>
            <column name="CREATED_AT" type="date"/>
            <column name="MODIFIED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-10">
        <createTable tableName="TAX_CODE">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false"/>
            </column>
            <column name="CODE" type="VARCHAR2(46 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_PAYER_NAME" type="VARCHAR2(100 BYTE)"/>
            <column name="TAX_PAYER_TYPE" type="VARCHAR2(5 BYTE)"/>
            <column name="STATUS" type="VARCHAR2(5 BYTE)"/>
            <column name="REGISTERED_AT" type="date"/>
            <column name="IDENTITY_NUMBER" type="VARCHAR2(12 BYTE)"/>
            <column name="TAX_AUTHORITY_CODE" type="VARCHAR2(10 BYTE)"/>
            <column name="CHAPTER_SUPPLY_CODE" type="VARCHAR2(10 BYTE)"/>
            <column name="CHAPTER_CODE" type="VARCHAR2(10 BYTE)"/>
            <column name="ADDRESS" type="VARCHAR2(100 BYTE)"/>
            <column name="PROVINCE" type="VARCHAR2(50 BYTE)"/>
            <column name="DISTRICT" type="VARCHAR2(50 BYTE)"/>
            <column name="WARD" type="VARCHAR2(50 BYTE)"/>
            <column defaultValueComputed="SYS_EXTRACT_UTC(SYSTIMESTAMP)" name="CREATED_AT" type="date"/>
            <column name="MODIFIED_AT" type="date"/>
            <column name="VALID_FROM" type="date"/>
            <column name="VALID_TO" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-11">
        <createTable tableName="INVOICE_PERMISSION_EVENT">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="INVOICE_PERMISSION_EVENT_PK"/>
            </column>
            <column name="CODE" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="ISSUER_CODE" type="VARCHAR2(5 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="VARCHAR2(3 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_NUMBER" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="NOTICE_DATE" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="VALID_FROM" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="VALID_TO" type="date"/>
            <column name="MODIFIED_AT" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="REF_MESSAGE_NUMBER" type="VARCHAR2(100 BYTE)"/>
            <column name="REF_NOTICE_DATE" type="date"/>
            <column defaultValueComputed="SYSDATE" name="CREATED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-12">
        <createTable tableName="INVOICE_PERMISSION">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;SEQ_INVOICE_PERMISSION&quot;.nextval" name="ID" type="NUMBER">
                <constraints nullable="false"/>
            </column>
            <column name="CODE" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="ISSUER_CODE" type="VARCHAR2(5 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="VARCHAR2(3 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_NUMBER" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="NOTICE_DATE" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="VALID_FROM" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="VALID_TO" type="date"/>
            <column name="MODIFIED_AT" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="CANCEL_MESSAGE_NUMBER" type="VARCHAR2(100 BYTE)"/>
            <column name="CANCEL_NOTICE_DATE" type="date"/>
            <column defaultValueComputed="SYSDATE" name="CREATED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-13">
        <createTable tableName="REGISTERED_INVOICE_TYPE_EVENT">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="REGISTED_INVOICE_TYPE_EVENT_PK"/>
            </column>
            <column name="CODE" type="VARCHAR2(46 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_AUTHORITY_CODE" type="VARCHAR2(5 BYTE)"/>
            <column name="INVOICE_WITH_CODE" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="VAT_INVOICE" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="SALE_INVOICE" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="PUBLIC_PROPERTY_SALE_INVOICE" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="NATIONAL_PROPERTY_SALE_INVOICE" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="OTHER_INVOICE" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="DOCUMENT" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="MODIFIED_AT" type="date"/>
            <column defaultValueComputed="SYSDATE" name="CREATED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-14">
        <createTable tableName="REGISTERED_INVOICE_TYPE">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="REGISTED_INVOICE_TYPE_PK"/>
            </column>
            <column name="CODE" type="VARCHAR2(46 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_AUTHORITY_CODE" type="VARCHAR2(5 BYTE)"/>
            <column name="INVOICE_WITH_CODE" type="NUMBER(1, 0)"/>
            <column name="VAT_INVOICE" type="NUMBER(1, 0)"/>
            <column name="SALE_INVOICE" type="NUMBER(1, 0)"/>
            <column name="PUBLIC_PROPERTY_SALE_INVOICE" type="NUMBER(1, 0)"/>
            <column name="NATIONAL_PROPERTY_SALE_INVOICE" type="NUMBER(1, 0)"/>
            <column name="OTHER_INVOICE" type="NUMBER(1, 0)"/>
            <column name="DOCUMENT" type="NUMBER(1, 0)"/>
            <column name="MODIFIED_AT" type="date"/>
            <column defaultValueComputed="SYSDATE" name="CREATED_AT" type="date"/>
            <column name="VALID_FROM" type="date"/>
            <column name="VALID_TO" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-15">
        <createTable tableName="BRANCH">
            <column autoIncrement="true" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="BRANCH_PK"/>
            </column>
            <column name="BRANCH_CODE" remarks="Mã chi nhánh" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="BRANCH_NAME" remarks="Tên chi nhánh" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="BRANCH_TAX_CODE" remarks="Mã số thuế chi nhánh" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="ADDRESS" remarks="Địa chỉ" type="VARCHAR2(500 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CONTACT_NAME" remarks="Tên người liên hệ" type="VARCHAR2(200 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" remarks="Trạng thái" type="NUMBER(38, 0)"/>
            <column name="CUSTOMER_TAX_CODE" remarks="Mã số thuế khách hàng" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="LEGAL_REPRESENTATIVE_NAME" remarks="Tên người đại diện pháp luật" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="LEGAL_REPRESENTATIVE_POSITION" remarks="Chức vụ người đại diện pháp luật" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="NOTE" remarks="Ghi chú" type="VARCHAR2(500 BYTE)"/>
            <column name="CONTACT_POSITION" remarks="Chức vụ người liên hệ" type="VARCHAR2(200 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CONTACT_PHONE" remarks="Số điện thoại người liên hệ" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CONTACT_EMAIL" remarks="Email người liên hệ" type="VARCHAR2(200 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CUSTOMER_CODE" remarks="Mã khách hàng" type="VARCHAR2(100 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-16">
        <createSequence maxValue="9999999999999999999999999999" sequenceName="CALLBACK_BATCH_SEQ" startValue="21"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-17">
        <createTable tableName="CALLBACK_BATCH">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;CALLBACK_BATCH_SEQ&quot;.nextval" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="CALLBACK_BATCH_PK"/>
            </column>
            <column name="STATUS" type="NUMBER(1, 0)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)"/>
            <column name="CALLBACK_ENDPOINT" type="VARCHAR2(100 BYTE)"/>
            <column name="CALLBACK_TIME" type="date"/>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)"/>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)"/>
            <column name="APP_ID" type="VARCHAR2(15 BYTE)"/>
            <column name="INIT_AT" type="date"/>
            <column name="IS_AUTHORIZED" type="NUMBER(1, 0)"/>
            <column name="TOTAL_SENT" type="NUMBER(5, 0)"/>
            <column name="TOTAL_RECEIVED" type="NUMBER(5, 0)"/>
            <column name="TOTAL_DEVIATION" type="NUMBER(5, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-18">
        <createSequence sequenceName="DIFFERENT_MESSAGE_REPORT_SEQ" startValue="1280"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-19">
        <createSequence maxValue="9999999999999999999999999999" sequenceName="INVOICE_APP_SEQ" startValue="512"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-20">
        <createSequence sequenceName="INVOICE_SEQ" startValue="1412280"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-21">
        <createSequence maxValue="9999999999999999999999999999" sequenceName="SEQ_EMAIL_CONFIG" startValue="41"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-22">
        <createSequence sequenceName="SEQ_INVOICE" startValue="60000"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-23">
        <createSequence cacheSize="0" maxValue="9999999999999999999999999999" sequenceName="SEQ_INVOICE_PERMISSION" startValue="133"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-24">
        <createSequence maxValue="9999999999999999999999999999" sequenceName="SEQ_SYSTEM_CONFIG" startValue="81"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-25">
        <createSequence maxValue="9999999999999999999999999999" sequenceName="SEQ_TOTAL_MESSAGE_REPORT" startValue="16323"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-26">
        <createSequence cacheSize="0" maxValue="9999999999999999999999999999" minValue="45133" sequenceName="TEST_INVOICE_SEQ" startValue="45133"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-27">
        <createTable tableName="AGENT">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="AGENT_PK"/>
            </column>
            <column name="CODE" remarks="Mã đại lý" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="NAME" remarks="Tên đại lý" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" remarks="MST đại lý" type="VARCHAR2(14 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="ADDRESS" remarks="Địa chỉ" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="REPRESENTATIVE" remarks="Người đại diện" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="REPRESENTATIVE_POSITION" remarks="Chức vụ người đại diện" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CONTACT_NAME" remarks="Người liên hệ" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CONTACT_POSITION" remarks="Chức vụ người liên hệ" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CONTACT_NUMBER" remarks="SĐT người liên hệ" type="VARCHAR2(10 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="EMAIL" remarks="Email người liên hệ" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CONTRACT_NUMBER" remarks="Số hợp đồng" type="VARCHAR2(50 BYTE)"/>
            <column name="CONTRACT_START_DATE" remarks="Ngày bắt đầu hợp đồng" type="date"/>
            <column name="CONTRACT_END_DATE" remarks="Ngày kết thúc hợp đồng" type="date"/>
            <column name="STATUS" remarks="0 - dừng hoạt động &#10;1 - hoạt động" type="NUMBER(1, 0)"/>
            <column name="CREATE_DATE" type="date"/>
            <column name="NOTE" type="VARCHAR2(300 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-28">
        <createTable tableName="AUTHORIZATION_SESSION">
            <column name="ID" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="AUTHORIZATION_SESSION_PK"/>
            </column>
            <column name="ENABLE_TIME" type="TIMESTAMP(6)"/>
            <column name="DISABLE_TIME" type="TIMESTAMP(6)"/>
            <column name="ENABLE_BY" type="VARCHAR2(100 BYTE)"/>
            <column name="DISABLE_BY" type="VARCHAR2(100 BYTE)"/>
            <column name="INTERNAL_SYNC_TOPIC" type="VARCHAR2(100 BYTE)"/>
            <column name="TOTAL_MESSAGE_500" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_503" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_504" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_999_OF_500_SUCCESS" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_999_OF_503_SUCCESS" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_999_OF_504_SUCCESS" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_999_OF_500_ERROR" type="NUMBER"/>
            <column defaultValueNumeric="0" name="STATUS" remarks="--not sync = 0&#10;--sync  = 1&#10;--sync_with_902 = 2" type="NUMBER(1, 0)"/>
            <column name="TOTAL_MESSAGE_999_OF_503_ERROR" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_999_OF_504_ERROR" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_1_OF_500" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_1_OF_503" type="NUMBER"/>
            <column name="TOTAL_MESSAGE_1_OF_504" type="NUMBER"/>
            <column name="MESSAGE_ID_902" type="VARCHAR2(100 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-29">
        <createTable tableName="CALLBACK_TRANSACTION">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="CALLBACK_ENDPOINT_PK"/>
            </column>
            <column name="STATUS" type="NUMBER(3, 0)"/>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="CALLBACK_URL" type="VARCHAR2(100 BYTE)"/>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)"/>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)"/>
            <column name="APP_ID" type="VARCHAR2(15 BYTE)"/>
            <column name="INIT_AT" type="date"/>
            <column name="IS_AUTHORIZED" type="NUMBER(1, 0)"/>
            <column name="CALLBACK_TIME" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-30">
        <createTable tableName="CALLBACK_TRANSACTION_DAN">
            <column name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="CALLBACK_ENDPOINT_DAN_PK"/>
            </column>
            <column name="STATUS" type="NUMBER(3, 0)"/>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="CALLBACK_URL" type="VARCHAR2(100 BYTE)"/>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)"/>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)"/>
            <column name="APP_ID" type="VARCHAR2(15 BYTE)"/>
            <column name="INIT_AT" type="date"/>
            <column name="IS_AUTHORIZED" type="NUMBER(1, 0)"/>
            <column name="CALLBACK_TIME" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-31">
        <createTable tableName="CALLBACK_TRANSACTION_ID">
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)"/>
            <column name="APP_ID" type="VARCHAR2(15 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-32">
        <createTable tableName="CONTRACT_INVOICE_APP">
            <column autoIncrement="true" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="CONTRACT_CONTRACT_INVOICE_APP_PK"/>
            </column>
            <column name="INVOICE_APP_ID" type="NUMBER"/>
            <column name="CONTRACT_ID" type="NUMBER"/>
            <column name="APP_ID" type="VARCHAR2(200 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-33">
        <createTable tableName="CONTRACT_TYPE">
            <column name="TYPE" type="NUMBER"/>
            <column name="DESCRIPTION" type="VARCHAR2(200 BYTE)"/>
            <column name="NAME" type="VARCHAR2(100 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-34">
        <createTable tableName="DAILY_REPORT_STATUS">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="DAILY_REPORT_LOG_PK"/>
            </column>
            <column name="REPORT_DATE" type="date"/>
            <column name="STATUS" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_ID" type="VARCHAR2(46 BYTE)"/>
            <column name="REPORT_TIME" type="TIMESTAMP(6)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-35">
        <createTable tableName="DATA_REPORT_HISTORY">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="DATA_REPORT_HISTORY_PK"/>
            </column>
            <column name="REPORT_MESSAGE_ID" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="RESPONSE_TYPE" type="VARCHAR2(15 BYTE)"/>
            <column name="TOTAL_SENT" type="NUMBER(5, 0)"/>
            <column name="TOTAL_RECEIVED" type="NUMBER(5, 0)"/>
            <column name="TOTAL_DEVIATION" type="NUMBER(5, 0)"/>
            <column name="REPORT_DATE" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-36">
        <createTable tableName="DIFFERENT_MESSAGE_REPORT">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;DIFFERENT_MESSAGE_REPORT_SEQ&quot;.nextval" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="DIFFERENT_MESSAGE_REPORT_PK"/>
            </column>
            <column name="REPORT_MESSAGE_ID" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="MISSING_MESSAGE_ID" type="VARCHAR2(50 BYTE)"/>
            <column name="TAX_CODE" type="VARCHAR2(50 BYTE)"/>
            <column name="REPORT_DATE" type="date"/>
            <column name="RECEIVED_TYPE" type="NUMBER(3, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-37">
        <createTable tableName="EMAIL_CONFIG">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;SEQ_EMAIL_CONFIG&quot;.&quot;NEXTVAL&quot;" name="ID" type="NUMBER(38, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="EMAIL_CONFIG_PK"/>
            </column>
            <column name="NAME" type="VARCHAR2(255 BYTE)"/>
            <column name="SUBJECT" type="VARCHAR2(1000 BYTE)"/>
            <column name="BODY" type="VARCHAR2(1000 BYTE)"/>
            <column name="EMAIL_TO" type="VARCHAR2(1000 BYTE)"/>
            <column name="EMAIL_CC" type="VARCHAR2(1000 BYTE)"/>
            <column name="USERNAME" type="VARCHAR2(255 BYTE)"/>
            <column name="PASSWORD" type="VARCHAR2(255 BYTE)"/>
            <column name="DESCRIPTION" type="VARCHAR2(255 BYTE)"/>
            <column name="IS_ACTIVE" type="NUMBER(1, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-38">
        <createTable tableName="EMAIL_HISTORY">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="EMAIL_HISTORY_PK"/>
            </column>
            <column name="SENT_AT" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="SENT_BY" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR2(4000 BYTE)"/>
            <column name="CUSTOMER_CODE" type="VARCHAR2(255 BYTE)"/>
            <column name="TYPE" type="VARCHAR2(255 BYTE)"/>
            <column name="REFERENCE_ID" type="NUMBER"/>
            <column name="REFERENCE_CODE" type="VARCHAR2(255 BYTE)"/>
            <column name="RECIPIENT" type="VARCHAR2(50 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-39">
        <createTable tableName="END_AUTHORIZATION_EVENT_STORE">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="END_AUTHORIZATION_EVENT_STORE_PK"/>
            </column>
            <column name="MESSAGE_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" remarks="500/503/504" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="REF_TRANSACTION_ID" remarks="EVENT_STORE.TRANSACTION_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="REF_MESSAGE_TYPE" remarks="EVENT_STORE.MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" remarks="init/sent/sent fail/success/error" type="VARCHAR2(32 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR2(4000 BYTE)"/>
            <column name="INIT_AT" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>
            <column name="REQUEST_AT" type="TIMESTAMP(6)"/>
            <column name="REF_INIT_AT" remarks="EVENT_STORE.INIT_AT" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>
            <column name="TCT_MESSAGE_ID" remarks="MTDiep của phản hồi 999/-1 của TCT" type="VARCHAR2(46 BYTE)"/>
            <column name="TCT_MESSAGE_TYPE" remarks="MLTDiep của phản hồi TCT" type="NUMBER(3, 0)"/>
            <column name="TCT_RECEPTION_STATUS" type="NUMBER(3, 0)"/>
            <column name="REF_TAX_CODE" type="VARCHAR2(15 BYTE)"/>
            <column name="SESSION_ID" type="VARCHAR2(50 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-40">
        <createTable tableName="ERROR_NOTIFICATION">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;ISEQ$$_222105&quot;.&quot;NEXTVAL&quot;" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ERROR_MESSAGE_PK"/>
            </column>
            <column name="SELLER_TAX_CODE" remarks="Mã số thuế người bán" type="VARCHAR2(15 BYTE)"/>
            <column name="INVOICE_TYPE" remarks="Kỹ hiệu mẫu số hóa đơn" type="VARCHAR2(5 BYTE)"/>
            <column name="INVOICE_SYMBOL" remarks="Ký hiệu hóa đơn" type="VARCHAR2(15 BYTE)"/>
            <column name="INVOICE_CREATED_DATE" remarks="Ngày lập" type="date"/>
            <column name="INVOICE_NUMBER" remarks="Số hóa đơn" type="NUMBER(8, 0)"/>
            <column name="MESSAGE_ID" remarks="Mã thông điệp" type="VARCHAR2(50 BYTE)"/>
            <column name="REASON" remarks="Lý do" type="VARCHAR2(1024 BYTE)"/>
            <column name="TAX_AUTHORITY_CODE" remarks="Mã của cơ quan thuế" type="VARCHAR2(50 BYTE)"/>
            <column name="STATUS" remarks="Trạng thái thông báo" type="NUMBER(5, 0)"/>
            <column name="APP_ID" remarks="ID của ứng dụng hóa đơn điện tử" type="VARCHAR2(15 BYTE)"/>
            <column name="CREATED_AT" remarks="Thời gian gửi lên TVAN" type="date"/>
            <column name="NOTIFICATION_PROPERTY" remarks="Tính chất thông báo" type="NUMBER(5, 0)"/>
            <column name="YEAR" remarks="Năm" type="NUMBER(5, 0)"/>
            <column name="RESPONSE_MESSAGE_ID" remarks="Mã thông điệp phản hồi" type="VARCHAR2(50 BYTE)"/>
            <column name="ERROR_DESCRIPTION" remarks="Mã lỗi, mô tả lỗi" type="VARCHAR2(1000 BYTE)"/>
            <column name="MESSAGE_TYPE" remarks="Mã loại thông điệp" type="NUMBER(5, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-41">
        <createTable tableName="ERROR_WARNING">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="ERROR_WARNING_PK"/>
            </column>
            <column name="STATUS" type="NUMBER(1, 0)"/>
            <column name="TRANSACTION_TYPE" remarks="Loại giao dịch" type="NUMBER(1, 0)"/>
            <column name="ERROR_CODE" remarks="Mã lỗi" type="VARCHAR2(50 BYTE)"/>
            <column name="ERROR_DESCRIPTION" remarks="Mô tả lỗi" type="NVARCHAR2(255)"/>
            <column name="EMAIL_TITLE" remarks="Tiêu đề email" type="VARCHAR2(255 BYTE)"/>
            <column name="EMAIL_CONTENT" remarks="Nội dung email" type="VARCHAR2(255 BYTE)"/>
            <column name="NUMBER_OF_ERRORS" remarks="Điều kiện số lượng lỗi để cảnh báo" type="NUMBER(2, 0)"/>
            <column name="ERROR_WARNING_PERIOD" remarks="Điều kiện trong khoảng thời gian" type="NUMBER(2, 0)"/>
            <column name="CREATED_AT" type="date"/>
            <column name="UPDATED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-42">
        <createTable tableName="INVOICE">
            <column name="TAX_AUTHORITY_CODE" remarks="Mã của cơ quan thuế" type="VARCHAR2(50 BYTE)"/>
            <column name="APP_ID" remarks="ID ứng dụng hóa đơn điện tử" type="VARCHAR2(15 BYTE)"/>
            <column name="TCGP_TAX_CODE" remarks="MST của tổ chức cung cấp giải pháp hóa đơn" type="VARCHAR2(15 BYTE)"/>
            <column name="BUYER_TAX_CODE" remarks="Mã số thuế người mua" type="VARCHAR2(15 BYTE)"/>
            <column name="MESSAGE_TYPE" remarks="Mã loại thông điệp" type="NUMBER(5, 0)"/>
            <column name="REF_INVOICE_SYMBOL" remarks="Ký hiệu hóa đơn liên quan" type="VARCHAR2(15 BYTE)"/>
            <column name="REF_INVOICE_TYPE" remarks="Loại hóa đơn liên quan" type="VARCHAR2(5 BYTE)"/>
            <column name="INVOICE_STATE" remarks="Trạng thái hóa đơn" type="NUMBER(1, 0)"/>
            <column name="SELLER_TAX_CODE" remarks="Mã số thuế người bán" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_ID" remarks="Mã thông điệp" type="VARCHAR2(50 BYTE)"/>
            <column name="REF_MESSAGE_ID" remarks="Mã thông điệp liên quan" type="VARCHAR2(50 BYTE)"/>
            <column name="ERROR_DESCRIPTION" remarks="Mã lỗi, mô tả lỗi" type="VARCHAR2(1000 BYTE)"/>
            <column name="SUMMARY_NUMBER" remarks="Số bảng tổng hợp dữ liệu (Số thứ tự bảng tổng hợp dữ liệu)" type="NUMBER(5, 0)"/>
            <column name="INVOICE_INDEX_NUMBER" remarks="Số thự tự dữ liệu trong thông điệp 400" type="NUMBER(5, 0)"/>
            <column name="RESPONSE_MESSAGE_ID" remarks="Mã thông điệp phản hồi" type="VARCHAR2(50 BYTE)"/>
            <column name="INVOICE_TYPE" remarks="Loại hóa đơn" type="VARCHAR2(5 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="INVOICE_SYMBOL" remarks="Ký hiệu hóa đơn" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="INVOICE_NUMBER" remarks="Số hóa đơn" type="NUMBER(8, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="INVOICE_CREATED_DATE" remarks="Ngày lập hóa đơn" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" remarks="Trạng thái truyền nhận" type="NUMBER(5, 0)"/>
            <column name="CREATED_AT" remarks="Ngày tạo" type="date"/>
            <column name="REF_INVOICE_NUMBER" remarks="Số hóa đơn của hóa đơn liên quan" type="NUMBER(8, 0)"/>
            <column name="REF_INVOICE_CREATED_DATE" remarks="Ngày lập hóa của đơn liên quan" type="date"/>
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;ISEQ$$_74129&quot;.&quot;NEXTVAL&quot;" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="INVOICE_TABLE_NUMBER_PK"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-43">
        <createTable tableName="INVOICE_APP">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;INVOICE_APP_SEQ&quot;.&quot;NEXTVAL&quot;" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="INVOICE_APP_PK"/>
            </column>
            <column name="CODE" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="NAME" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="STATUS" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="API_KEY" type="VARCHAR2(36 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="CALLBACK_ENDPOINT" type="VARCHAR2(100 BYTE)"/>
            <column name="CREATED_AT" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="PHONE" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="EMAIL" type="VARCHAR2(100 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="APP_ID" type="VARCHAR2(50 BYTE)"/>
            <column name="DESCRIPTION" type="VARCHAR2(500 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-44">
        <createTable tableName="INVOICE_REPORT">
            <column autoIncrement="true" defaultOnNull="true" generationType="BY DEFAULT" name="ID" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_RESULTTAXREPORT01"/>
            </column>
            <column defaultValueComputed="SYSTIMESTAMP" name="CREATIONTIME" type="TIMESTAMP(7)"/>
            <column name="TAXREPORTCREATIONTIME" type="TIMESTAMP(7)">
                <constraints nullable="false"/>
            </column>
            <column name="TAXCODE" type="VARCHAR2(14 BYTE)"/>
            <column name="TVANMESSCODE" type="VARCHAR2(46 BYTE)"/>
            <column name="TCTMESSCODE" type="VARCHAR2(46 BYTE)"/>
            <column name="YEAR" type="NUMBER(5, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="QUARTER" type="NUMBER(5, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="MONTH" type="NUMBER(5, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="TAXREPORTNUMBER" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="TEMPLATENO" type="VARCHAR2(11 BYTE)"/>
            <column name="INVOICENO" type="VARCHAR2(8 BYTE)"/>
            <column name="INVOICENUMBER" type="NUMBER(10, 0)"/>
            <column name="SERIALNO" type="VARCHAR2(8 BYTE)"/>
            <column name="INVOICEDATE" type="TIMESTAMP(7)"/>
            <column name="INVOICEREFERENCENUMBER" type="VARCHAR2(8 BYTE)"/>
            <column name="INVOICEREFERENCETEMPLATENO" type="VARCHAR2(11 BYTE)"/>
            <column name="INVOICEREFERENCESERIALNO" type="VARCHAR2(8 BYTE)"/>
            <column name="INVOICEREFERENCEDATE" type="TIMESTAMP(7)"/>
            <column name="MTLOI" type="VARCHAR2(2000 BYTE)"/>
            <column name="MLOI" type="VARCHAR2(4 BYTE)"/>
            <column name="LTBAO" type="NUMBER(5, 0)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-45">
        <createTable tableName="MISSING_MESSAGE">
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="MISSING_MESSAGE_PK"/>
            </column>
            <column name="EXPECTED_TYPE" type="NUMBER"/>
            <column defaultValueComputed="SYSDATE" name="CREATED_AT" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="REQUEST_TYPE" type="NUMBER(3, 0)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)"/>
            <column name="APP_ID" type="VARCHAR2(15 BYTE)"/>
            <column name="IS_AUTHORIZED" type="NUMBER(1, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-46">
        <createTable tableName="NOT_SUCCESS_MESSAGE_REPORT">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="NOT_SUCCESS_MESSAGE_REPORT_PK"/>
            </column>
            <column name="REPORT_MESSAGE_ID" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_ID" type="VARCHAR2(50 BYTE)"/>
            <column name="STATUS" remarks="0 - Lỗi&#10;1 - Không tiếp nhận&#10;2 - Không chấp nhận" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(50 BYTE)"/>
            <column name="REPORT_DATE" type="date"/>
            <column name="RESPONSE_TYPE" type="NUMBER(3, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-47">
        <createTable tableName="PORTAL_USER">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PORTAL_USER_PK"/>
            </column>
            <column name="CUSTOMER_CODE" type="VARCHAR2(50 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="EMAIL" type="VARCHAR2(255 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="PASSWORD" type="VARCHAR2(1000 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="CREATED_AT" type="date"/>
            <column name="UPDATED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-48">
        <createTable tableName="REGISTERED_DIGITAL_SIGNATURE">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="REGISTED_SIGNATURE_PK"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(15 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="SERI" type="VARCHAR2(40 BYTE)"/>
            <column name="FROM_DATE" type="date"/>
            <column name="TO_DATE" type="date"/>
            <column name="STATUS" type="NUMBER(5, 0)"/>
            <column name="INVOICE_TYPE" remarks="Loại hóa đơn" type="NUMBER(5, 0)"/>
            <column name="TRANSMISSION_METHOD" remarks="Phương thức chuyển dữ liệu" type="NUMBER(5, 0)"/>
            <column name="INVOICE_FORM" remarks="Hình thức hóa đơn" type="NUMBER(5, 0)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-49">
        <createTable tableName="REPORT_DOCUMENT_SUPPORTER">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="REPORT_DOCUMENT_SUPPORTER"/>
            </column>
            <column name="TRANSACTIN_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="INIT_AT" type="date"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-50">
        <createTable tableName="REQUEST_INFO">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="REQUEST_INFO_PK"/>
            </column>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TRANSACTION_ID_HASH" type="NUMBER(10, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="CREATED_AT" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="APP_ID" type="VARCHAR2(50 BYTE)"/>
            <column name="QUANTITY" type="NUMBER(5, 0)"/>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)"/>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="INIT_AT" type="date"/>
            <column name="DELETED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-51">
        <createTable tableName="SUMMARY_INVOICE_INFO">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SUMMARY_INVOICE_INFO_PK"/>
            </column>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="RESPONSE_TYPE" type="NUMBER(3, 0)"/>
            <column name="CREATED_AT" remarks="Ngày gửi hóa đơn lên TVAN" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="STATUS" type="NUMBER(5, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="NUMBER_OF_INVOICES" remarks="Số lượng hóa đơn gửi lên" type="NUMBER(5, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="NUMBER_OF_ACCEPTED_INVOICES" remarks="Số lượng hóa đơn được tiếp nhận" type="NUMBER(5, 0)"/>
            <column name="NUMBER_OF_REJECTED_INVOICES" remarks="Số lượng hóa đơn bị từ chối/lỗi" type="NUMBER(5, 0)"/>
            <column name="DATA_PERIOD_TYPE" remarks="Loại kỳ dữ liệu" type="NUMBER(3, 0)"/>
            <column name="DATA_PERIOD" remarks="Kỳ dữ liệu" type="VARCHAR2(10 BYTE)"/>
            <column name="SENT_NO" remarks="Lần lại gửi số" type="NUMBER(3, 0)"/>
            <column name="INVOICE_CREATED_DATE" remarks="Ngày tạo hóa đơn" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="DELETED_AT" type="date"/>
            <column name="APP_ID" type="VARCHAR2(50 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-52">
        <createTable tableName="SYSTEM_CONFIG">
            <column defaultValueComputed="&quot;TVAN_TST&quot;.&quot;SEQ_SYSTEM_CONFIG&quot;.&quot;NEXTVAL&quot;" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYSTEM_CONFIG_PK"/>
            </column>
            <column name="NAME" type="VARCHAR2(50 BYTE)"/>
            <column name="VALUE" type="VARCHAR2(50 BYTE)"/>
            <column name="DESCRIPTION" type="VARCHAR2(100 BYTE)"/>
            <column name="CREATED_AT" type="date"/>
            <column name="CREATED_USER" type="VARCHAR2(50 BYTE)"/>
            <column name="MODIFIED_USER" type="VARCHAR2(50 BYTE)"/>
            <column name="TYPE" remarks="0 - not text&#10;1 - text" type="VARCHAR2(2 BYTE)"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-53">
        <createTable tableName="TRANSACTION_ERROR">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="TRANSACTION_ERROR_PK"/>
            </column>
            <column name="TRANSACTION_ID" type="VARCHAR2(46 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="TAX_CODE" type="VARCHAR2(14 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="MESSAGE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="RESPONSE_TYPE" type="NUMBER(3, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="RECEPTION_STATUS" type="NUMBER(1, 0)"/>
            <column name="CREATED_AT" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR2(4000 BYTE)"/>
            <column name="STATUS" type="NUMBER(1, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="ERROR_CODE" type="VARCHAR2(10 BYTE)"/>
            <column name="UPDATED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-54">
        <createTable tableName="UPDATE_HISTORY">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="AGENT_UPDATE_HISTORY_PK"/>
            </column>
            <column name="UPDATE_DATE" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="UPDATE_USER" type="VARCHAR2(500 BYTE)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR2(1000 BYTE)"/>
            <column name="AGENT_ID" type="NUMBER"/>
            <column name="CUSTOMER_ID" type="NUMBER"/>
            <column name="BRANCH_ID" type="NUMBER"/>
            <column name="INVOICE_APP_ID" type="NUMBER"/>
            <column name="TYPE" remarks="1 : agent&#10;&#10;2 : customer&#10;&#10;3 : branch&#10;&#10;4 : invoice app" type="NUMBER"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-55">
        <createTable tableName="USER_ERROR_WARNING">
            <column autoIncrement="true" defaultOnNull="false" generationType="ALWAYS" name="ID" type="NUMBER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="USER_ERROR_WARNING_PK"/>
            </column>
            <column name="STATUS" type="NUMBER(1, 0)"/>
            <column name="ERROR_WARNING_ID" type="NUMBER"/>
            <column name="NAME" type="NVARCHAR2(255)"/>
            <column name="EMAIL" type="VARCHAR2(255 BYTE)"/>
            <column name="CREATED_AT" type="date"/>
            <column name="IS_DELETED" type="NUMBER(1, 0)"/>
            <column name="UPDATED_AT" type="date"/>
        </createTable>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-56">
        <createIndex indexName="BRANCH_TAX_CODE_UK" tableName="BRANCH" unique="true">
            <column name="BRANCH_TAX_CODE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-57">
        <addUniqueConstraint columnNames="BRANCH_TAX_CODE" constraintName="BRANCH_TAX_CODE_UK" forIndexName="BRANCH_TAX_CODE_UK" tableName="BRANCH"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-58">
        <createIndex indexName="INVOICE_APP_API_KEY_UK" tableName="INVOICE_APP" unique="true">
            <column name="API_KEY"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-59">
        <addUniqueConstraint columnNames="API_KEY" constraintName="INVOICE_APP_API_KEY_UK" forIndexName="INVOICE_APP_API_KEY_UK" tableName="INVOICE_APP"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-60">
        <createIndex indexName="INVOICE_PERMISSION_EVENT_UN" tableName="INVOICE_PERMISSION_EVENT" unique="true">
            <column name="CODE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-61">
        <addUniqueConstraint columnNames="CODE" constraintName="INVOICE_PERMISSION_EVENT_UN" forIndexName="INVOICE_PERMISSION_EVENT_UN" tableName="INVOICE_PERMISSION_EVENT"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-62">
        <createIndex indexName="SYS_C0030536" tableName="SYSTEM_CONFIG" unique="true">
            <column name="NAME"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-63">
        <addUniqueConstraint columnNames="NAME" constraintName="SYS_C0030536" forIndexName="SYS_C0030536" tableName="SYSTEM_CONFIG"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-64">
        <createIndex indexName="SYS_C008646" tableName="CUSTOMER" unique="true">
            <column name="CODE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-65">
        <addUniqueConstraint columnNames="CODE" constraintName="SYS_C008646" forIndexName="SYS_C008646" tableName="CUSTOMER"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-66">
        <createIndex indexName="SYS_C008652" tableName="ABOUT_TO_EXPIRE_CUSTOMER" unique="true">
            <column name="CODE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-67">
        <addUniqueConstraint columnNames="CODE" constraintName="SYS_C008652" forIndexName="SYS_C008652" tableName="ABOUT_TO_EXPIRE_CUSTOMER"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-68">
        <createIndex indexName="SYS_C008739" tableName="CONTRACT" unique="true">
            <column name="CONTRACT_NUMBER"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-69">
        <addUniqueConstraint columnNames="CONTRACT_NUMBER" constraintName="SYS_C008739" forIndexName="SYS_C008739" tableName="CONTRACT"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-70">
        <createIndex indexName="SYS_C008757" tableName="CONTRACT_TYPE" unique="true">
            <column name="TYPE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-71">
        <addUniqueConstraint columnNames="TYPE" constraintName="SYS_C008757" forIndexName="SYS_C008757" tableName="CONTRACT_TYPE"/>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-72">
        <createIndex indexName="CALLBACK_ID_TRANSACTION_ID_IDX" tableName="CALLBACK_TRANSACTION_ID">
            <column name="TRANSACTION_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-73">
        <createIndex indexName="CALLBACK_TRANSACTION_TRANSACTION_ID_IDX" tableName="CALLBACK_TRANSACTION">
            <column name="TRANSACTION_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-74">
        <createIndex indexName="DAILY_MESSAGE_REPORT_TCT_ID_IDX" tableName="DAILY_MESSAGE_REPORT_TCT" unique="true">
            <column name="REPORT_DATE"/>
            <column name="MESSAGE_TYPE"/>
            <column name="RESPONSE_TYPE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-75">
        <createIndex indexName="END_AUTHORIZATION_EVENT_STORE_TRANSACTION_ID_IDX" tableName="END_AUTHORIZATION_EVENT_STORE">
            <column name="MESSAGE_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-76">
        <createIndex indexName="INVOICE_APP_APP_ID_IDX" tableName="INVOICE_APP" unique="true">
            <column name="APP_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-77">
        <createIndex indexName="INVOICE_TABLE_GLOBAL_PARTITION_IDX" tableName="INVOICE">
            <column name="SELLER_TAX_CODE"/>
            <column name="INVOICE_SYMBOL"/>
            <column name="INVOICE_NUMBER"/>
            <column name="INVOICE_TYPE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-78">
        <createIndex indexName="INVOICE_TABLE_MESSAGE_ID_IDX" tableName="INVOICE">
            <column name="MESSAGE_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-79">
        <createIndex indexName="MESSAGE_ID_IDX" tableName="NOT_SUCCESS_MESSAGE_REPORT">
            <column name="MESSAGE_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-80">
        <createIndex indexName="MISSING_MESSAGE_ID_IDX" tableName="DIFFERENT_MESSAGE_REPORT">
            <column name="MISSING_MESSAGE_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-81">
        <createIndex indexName="REGISTED_INVOICE_TYPE_TAX_CODE_IDX" tableName="REGISTERED_INVOICE_TYPE">
            <column name="TAX_CODE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-82">
        <createIndex indexName="SUMMARY_INVOICE_INFO_TRANSACTION_ID_IDX" tableName="SUMMARY_INVOICE_INFO">
            <column name="CREATED_AT"/>
            <column name="TRANSACTION_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-83">
        <createIndex indexName="TAX_CODE_EVENT_TAX_CODE_INDEX" tableName="TAX_CODE_EVENT">
            <column name="TAX_CODE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-84">
        <createIndex indexName="TAX_CODE_TAX_CODE_IDX" tableName="TAX_CODE">
            <column name="TAX_CODE"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-85">
        <createIndex indexName="TRANSACTION_ERROR_TRANSACTION_ID_IDX" tableName="TRANSACTION_ERROR">
            <column name="CREATED_AT"/>
            <column name="TRANSACTION_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="danpq (generated)" id="1709631699827-86">
        <createIndex indexName="TRANSACTION_TRANSACTION_ID_IDX" tableName="TRANSACTION" unique="true">
            <column name="TRANSACTION_ID"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
