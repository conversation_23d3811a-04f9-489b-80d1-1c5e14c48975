package vn.vnpay.tvan.apps.reconciliation.service.dto;


import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Data
@ToString
public class MissingMessageSummaryDTO {
    private List<String> s100m999s;
    private List<String> s100m102s;
    private List<String> s100m103s;
    private List<String> s200m999s;
    private List<String> s200m202s;
    private List<String> s200m204s;
    private List<String> s203m999s;
    private List<String> s203m204s;
    private List<String> s206m999s;
    private List<String> s206m204s;
    private List<String> s300m999s;
    private List<String> s300m204s;
    private List<String> s300m301s;
    private List<String> s400m999s;
    private List<String> s400m204s;

    public MissingMessageSummaryDTO() {
        s100m999s = List.of("");
        s100m102s = List.of("");
        s100m103s = List.of("");
        s200m999s = List.of("");
        s200m202s = List.of("");
        s200m204s = List.of("");
        s203m999s = List.of("");
        s203m204s = List.of("");
        s300m999s = List.of("");
        s300m204s = List.of("");
        s300m301s = List.of("");
        s400m999s = List.of("");
        s400m204s = List.of("");
    }
}
