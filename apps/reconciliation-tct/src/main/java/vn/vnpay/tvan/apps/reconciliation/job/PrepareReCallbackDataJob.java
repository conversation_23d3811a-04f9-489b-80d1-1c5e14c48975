package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.CallbackService;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;

@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.prepare-recallback-data", havingValue = "true")
@Slf4j
@RequiredArgsConstructor
public class PrepareReCallbackDataJob {

    private final CallbackService callbackService;

    @Scheduled(cron = "${scheduler.job.cron.prepare-recallback-data}")
    public void prepareCallbackData() {
        long startTime = System.currentTimeMillis();
        MDC.put(TRACE_ID, String.valueOf(startTime));

        callbackService.prepareReCallbackData();

        log.info("Prepare callback data execute time = {}ms", System.currentTimeMillis() - startTime);
        MDC.clear();
    }
}
