package vn.vnpay.tvan.apps.reconciliation.job;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.TCTReportService;


@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.tct-reconciliation", havingValue = "true")
public class TCTReport {

    private final TCTReportService tctReportService;
    public TCTReport(TCTReportService tctReportService) {
        this.tctReportService = tctReportService;
    }

    @Scheduled(cron = "${scheduler.job.cron.tct-reconciliation}")
    public void sendReportEmail() {
        tctReportService.generateTCTReport();
    }

}
