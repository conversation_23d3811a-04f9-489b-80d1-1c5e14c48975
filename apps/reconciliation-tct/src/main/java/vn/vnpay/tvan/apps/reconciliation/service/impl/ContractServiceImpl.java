package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.vnpay.tvan.apps.reconciliation.repository.AboutToExpireCustomerRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.ContractRepository;
import vn.vnpay.tvan.apps.reconciliation.repository.CustomerRepository;
import vn.vnpay.tvan.apps.reconciliation.service.ContractService;
import vn.vnpay.tvan.libs.common.constant.ContractStatus;
import vn.vnpay.tvan.libs.common.constant.ContractType;
import vn.vnpay.tvan.libs.common.constant.PaymentType;
import vn.vnpay.tvan.libs.common.enumeration.AboutToExpireCustomerStatus;
import vn.vnpay.tvan.libs.common.enumeration.CustomerStatus;
import vn.vnpay.tvan.libs.common.model.entity.Contract;
import vn.vnpay.tvan.libs.common.model.entity.Customer;
import vn.vnpay.tvan.libs.common.util.DatetimeUtil;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static vn.vnpay.tvan.libs.common.util.DatetimeUtil.SHORT_DATE;
import static vn.vnpay.tvan.libs.common.util.DatetimeUtil.convertInstantToShortDate;
import static vn.vnpay.tvan.libs.common.util.DatetimeUtil.convertInstantToStringWithDefaultZone;

@Service
@Slf4j
public class ContractServiceImpl implements ContractService {
    private final ContractRepository contractRepository;
    private final CustomerRepository customerRepository;
    private final AboutToExpireCustomerRepository aboutToExpireCustomerRepository;

    public ContractServiceImpl(ContractRepository contractRepository,
                               CustomerRepository customerRepository,
                               AboutToExpireCustomerRepository aboutToExpireCustomerRepository) {
        this.contractRepository = contractRepository;
        this.customerRepository = customerRepository;
        this.aboutToExpireCustomerRepository = aboutToExpireCustomerRepository;
    }

    @Override
    public void updateContractStatus() {
        /*Lấy danh sách hợp đồng chờ hoạt động*/
        List<Contract> comingSoonContracts = contractRepository
                .getAllComingSoonContracts();
        log.info("Coming soon contracts have not active yet size = "+ comingSoonContracts.size());

        List<Contract> activatableContracts = enrichContractActivatable(comingSoonContracts);
        log.info("Activatable contracts size = "+ activatableContracts.size());

        List<Long> activatableContractsCusId = activatableContracts.stream()
                .map(Contract::getCustomerId).distinct().toList();

        /*-	Case 1 : KH có tồn tại 1 gói hợp đồng có trạng thái Chờ hoạt động*/
        List<Contract> expiredContractsExistComingSoonStatus = contractRepository
                .getAllExpireContractByCustomerId(activatableContractsCusId);
        log.info("Case 1: exist 1 contract coming soon status size = "+ expiredContractsExistComingSoonStatus.size());

        /*-	Case 2: KH không có hợp đồng tiếp theo có trạng thái “Chờ hoạt động”*/
        List<Contract> expiredContractsWithoutComingSoonStatus =
                contractRepository.getExpiredContractsWithoutComingSoonStatus();
        log.info("Case 2: does not exist 1 contract coming soon status size = "
                + expiredContractsWithoutComingSoonStatus.size());

        List<Contract> expirableContracts = new ArrayList<>();

        handleCase1(expiredContractsExistComingSoonStatus,expirableContracts, activatableContracts,
                activatableContractsCusId);

        handleCase2(expiredContractsWithoutComingSoonStatus,expirableContracts);

        expirableContracts.addAll(activatableContracts);
        log.info("All data need updating size = " + expirableContracts.size());

        contractRepository.saveAll(expirableContracts);
    }

    private void handleCase1(List<Contract> expiredContractsExistComingSoonStatus, List<Contract> expirableContracts,
                             List<Contract> activatableContracts, List<Long> activatableContractsCusId) {
        log.info("Handling case 1");
        expiredContractsExistComingSoonStatus.forEach(x -> x.setStatus(ContractStatus.EXPIRED.value()));
        expirableContracts.addAll(expiredContractsExistComingSoonStatus);

        activatableContracts.forEach(x -> x.setStatus(ContractStatus.ACTIVE.value()));

        updateCustomerActiveContractId(activatableContractsCusId, activatableContracts);
    }

    private void handleCase2(List<Contract> expiredContractsWithoutComingSoonStatus, List<Contract> expirableContracts) {
        log.info("Handling case 2");
        expiredContractsWithoutComingSoonStatus.forEach(x -> x.setStatus(ContractStatus.EXPIRED.value()));
        expirableContracts.addAll(expiredContractsWithoutComingSoonStatus);
        List<String> customerCodes = expiredContractsWithoutComingSoonStatus.stream().map(Contract::getCustomerCode).toList();
        log.info("Customer code need to expired size = "+ customerCodes.size());

        setCustomerToExpired(customerCodes);
        setAboutToExpireToExpired(customerCodes);
    }

    private void setCustomerToExpired(List<String> customerCodes) {
        log.info("Started updating customer's status!");
        customerRepository.updateCustomerStatus(customerCodes, CustomerStatus.INACTIVE.value());
    }
    private void setAboutToExpireToExpired(List<String> customerCodes) {
        log.info("Started updating about to expired customer's status!");
        aboutToExpireCustomerRepository.updateCustomerStatus(customerCodes, AboutToExpireCustomerStatus.EXPIRED.code());
    }

    private boolean contractTypeIsYearOrYearTurnAndTodayIsContractStartDate(Contract contract) {
        String today = convertInstantToStringWithDefaultZone(Instant.now(), SHORT_DATE);

        return (contract.getContractType() == ContractType.YEAR_TURN.code() ||
                (contract.getContractType() == ContractType.YEAR.code() || contract.getPaymentType() == PaymentType.POSTPAID.value()))
                && convertInstantToShortDate(contract.getFromDate()).equals(today);
    }

    private List<Contract> enrichContractActivatable(List<Contract> contract) {
        return contract.stream().filter(this::contractTypeIsYearOrYearTurnAndTodayIsContractStartDate).toList();
    }

    private void updateCustomerActiveContractId(List<Long> activatableContractsCusId,
                                                List<Contract> activatableContracts) {
        log.info("Started getting customer's info");
        List<Customer> customers = customerRepository.findAllById(activatableContractsCusId);

        log.info("Started setting new active contract id of customer");
        for(Contract contract: activatableContracts){
            for (Customer customer: customers){
                if(Objects.equals(contract.getCustomerId(), customer.getId())){
                    customer.setActiveContractId(contract.getId());
                }
            }
        }

        customerRepository.saveAll(customers);
    }
}
