package vn.vnpay.tvan.apps.reconciliation.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@ToString
public class AuthorizeReconciliationResultModel {
    private int sumTotalMessage500;
    private int sumTotalMessage503;
    private int sumTotalMessage504;
    private int sumTotalMessage999Of500Success;
    private int sumTotalMessage999Of503Success;
    private int sumTotalMessage999Of504Success;
    private int sumTotalMessage999Of500Error;
    private int sumTotalMessage999Of503Error;
    private int sumTotalMessage999Of504Error;
    private int sumTotalMessage1Of500;
    private int sumTotalMessage1Of503;
    private int sumTotalMessage1Of504;
    private String missingMessage500;
    private String missingMessage503;
    private String missingMessage504;
    private int status;
    private String sessionId;
}
