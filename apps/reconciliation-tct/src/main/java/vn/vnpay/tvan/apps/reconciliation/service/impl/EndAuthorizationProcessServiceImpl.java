package vn.vnpay.tvan.apps.reconciliation.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import vn.vnpay.share.message.constant.MessageType;
import vn.vnpay.tvan.apps.reconciliation.repository.EndAuthorizationEventStoreRepository;
import vn.vnpay.tvan.apps.reconciliation.service.CacheService;
import vn.vnpay.tvan.apps.reconciliation.service.EndAuthorizationProcessService;
import vn.vnpay.tvan.apps.reconciliation.service.EventService;
import vn.vnpay.tvan.apps.reconciliation.service.MinioService;
import vn.vnpay.tvan.libs.common.constant.Constant;
import vn.vnpay.tvan.libs.common.constant.EndAuthorizationEventStatus;
import vn.vnpay.tvan.libs.common.constant.EventType;
import vn.vnpay.tvan.libs.common.exception.EndedAuthorizedException;
import vn.vnpay.tvan.libs.common.model.Event;
import vn.vnpay.tvan.libs.common.model.RequestInfo;
import vn.vnpay.tvan.libs.common.model.entity.EndAuthorizationEventStore;
import vn.vnpay.tvan.libs.common.model.entity.EventStore;
import vn.vnpay.tvan.libs.common.util.DecodeStringUtil;
import vn.vnpay.tvan.libs.common.util.MdcListenableFutureCallback;
import vn.vnpay.tvan.libs.common.util.Utils;
import vn.vnpay.tvan.libs.common.util.XmlUtil;

import java.time.Instant;
import java.util.List;

@Slf4j
@Service("EndAuthorizationProcessService")
public class EndAuthorizationProcessServiceImpl implements EndAuthorizationProcessService {

    private final EndAuthorizationEventStoreRepository repository;

    private final MinioService minioService;

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final EventService eventService;

    private final CacheService cacheService;
    @Value("${app.pban}")
    private String pBan;

    @Value("${app.taxCode}")
    private String tvanTaxCode;

    @Value("${app.tctTaxCode}")
    private String tctTaxCode;

    @Value("${spring.kafka.topics.direct-topics-in}")
    private String kafkaQueueIn;

    private final String MESSAGE_TYPE_PLACE_HOLDER = "messageType";
    private final String TEMP_PLACE_HOLDER = "temp";

    public EndAuthorizationProcessServiceImpl(EndAuthorizationEventStoreRepository repository,
                                              MinioService minioService,
                                              @Qualifier("tctDirectKafkaTemplate") KafkaTemplate<String, String> kafkaTemplate,
                                              EventService eventService,
                                              CacheService cacheService) {
        this.repository = repository;
        this.minioService = minioService;
        this.kafkaTemplate = kafkaTemplate;
        this.eventService = eventService;
        this.cacheService = cacheService;
    }

    @Override
    public void sendMessageToTCT(Event<Object> event, String topic) {
        String sourceEventTransactionId = event.getTransactionId(); //transactionId của source message 202/204 đã từng trả về cho EINVOICE
        Instant sourceEventInit = event.getInitAt();
        MessageType sourceEventMessageType = event.getMessageType();
        EventType sourceEventType = event.getType();
        String refTaxCode = event.getTaxCode();

        String sessionId = topic.substring(topic.indexOf("_") + 1, topic.length());

        MDC.put(Constant.KEY.TRANSACTION_ID, sourceEventTransactionId);

        //Đây ko phải là event để push event, chỉ là event dùng để get data từ minio
        EventStore eventStore = EventStore.builder()
                .transactionId(sourceEventTransactionId)
                .initAt(sourceEventInit)
                .taxCode(event.getTaxCode())
                .messageType(sourceEventMessageType.code())
                .type(sourceEventType.index())
                .build();

        List<MessageType> messageTypes = mappingListMessageType(sourceEventMessageType);

        messageTypes.forEach(messageType -> {
            log.info("Start sending :" + messageType);
            //Tạo ra 1 bản ghi EndAuthorizationEventStore với status = init
            String transactionId = tvanTaxCode + Utils.upperCaseUUIDWithoutDash(); //transactionId của message 500/504 chuẩn bị gửi đến TCT
            EndAuthorizationEventStore endAuthorizationEventStore = initEndAuthorizationEventStore(transactionId, messageType,
                    sourceEventTransactionId, sourceEventMessageType, sourceEventInit, refTaxCode, sessionId);

            EventType eventType = getEventTypeByMessageType(messageType);
            String xml = DecodeStringUtil.decodeBase64Data(getBase64StringFromEventStore(eventStore, eventType));
            String tctXmlRequest = generateTDiepFromSourceXml(xml, messageType, transactionId);
            sendRequest(tctXmlRequest, endAuthorizationEventStore, event, topic);
        });
    }

    private EndAuthorizationEventStore initEndAuthorizationEventStore(String transactionId,
                                                                      MessageType messageType,
                                                                      String refTransactionId,
                                                                      MessageType refMessageType,
                                                                      Instant refInitAt,
                                                                      String refTaxCode,
                                                                      String sessionId) {
        return repository.save(EndAuthorizationEventStore.builder()
                .messageId(transactionId)
                .messageType(messageType.code())
                .refTransactionId(refTransactionId)
                .refMessageType(refMessageType.code())
                .refInitAt(refInitAt)
                .initAt(Instant.now())
                .status(EndAuthorizationEventStatus.INIT.value())
                .refTaxCode(refTaxCode)
                .sessionId(sessionId)
                .build());
    }

    private String getBase64StringFromEventStore(EventStore eventStore, EventType eventType) {
        return minioService.getData(eventStore, eventType);
    }

    private String generateTDiepFromSourceXml(String xml, MessageType messageType, String transactionId) {
        String dLieu;
        if (messageType == MessageType._503_ThongDiepChuyenHoaDonKhongDuDieuKienCapMa) {
            dLieu = beginTag(Constant.XML_TAG.DATA) + getTagFromSourceXml(xml, Constant.XML_TAG.INVOICE) + endTag(Constant.XML_TAG.DATA);
        } else {
            dLieu = getTagFromSourceXml(xml, Constant.XML_TAG.DATA);
        }
        String newTTChungXml = generateTTChung(xml, messageType, transactionId);
        return beginTag(Constant.XML_TAG.MESSAGE) + newTTChungXml + dLieu + endTag(Constant.XML_TAG.MESSAGE);
    }

    private String getTagFromSourceXml(String xml, String tag) throws EndedAuthorizedException {
        String data = getOuterXmlUsingSubString(tag, xml);
        if (ObjectUtils.isEmpty(data)) {
            throw new EndedAuthorizedException("Not Found Tag " + tag + " in data XML of transaction : ");
        } else {
            return data;
        }
    }

    private String getInnerTagFromSourceXml(String xml, String tag) throws EndedAuthorizedException {
        String data = XmlUtil.getInnerXmlUsingSubString(tag, xml);
        if (ObjectUtils.isEmpty(data)) {
            throw new EndedAuthorizedException("Not Found Tag " + tag + " in data XML of transaction : ");
        } else {
            return data;
        }
    }

    private String generateTTChung(String xml, MessageType messageType, String transactionId) {
        String tempTTChung = generateTempTTChung(transactionId);
        return generateTTChungFromTemp(xml, tempTTChung, messageType);
    }

    private String generateTempTTChung(String transactionId) {

        return beginTag(Constant.XML_TAG.GENERAL_INFO) +
                beginTag(Constant.XML_TAG.VERSION) + pBan + endTag(Constant.XML_TAG.VERSION) +
                beginTag(Constant.XML_TAG.SENDER_ID) + tvanTaxCode + endTag(Constant.XML_TAG.SENDER_ID) +
                beginTag(Constant.XML_TAG.RECEIVER_ID) + tctTaxCode + endTag(Constant.XML_TAG.RECEIVER_ID) +
                beginTag(Constant.XML_TAG.MESSAGE_ID) + transactionId + endTag(Constant.XML_TAG.MESSAGE_ID) +
                beginTag(Constant.XML_TAG.MESSAGE_TYPE) + MESSAGE_TYPE_PLACE_HOLDER + endTag(Constant.XML_TAG.MESSAGE_TYPE) +
                TEMP_PLACE_HOLDER +
                endTag(Constant.XML_TAG.GENERAL_INFO);
    }

    private String generateTTChungFromTemp(String xml, String tempTTChung, MessageType messageType) {
        String newTTChungXml = "";

        newTTChungXml = tempTTChung
                    .replace(TEMP_PLACE_HOLDER,beginTag(Constant.XML_TAG.QUANTITY) +
                            //flow ủy quyền chỉ có hóa đơn 200, dẫn đến số lượng luôn là 1
                            1 +
                            endTag(Constant.XML_TAG.QUANTITY))
                    .replace(MESSAGE_TYPE_PLACE_HOLDER, messageType.code().toString());

        return newTTChungXml;
    }

    private void sendRequest(String requestXml,
                             EndAuthorizationEventStore endAuthorizationEventStore,
                             Event<Object> event,
                             String topic) {
        log.info("Start sendRequest to TCT");

        //Đôi khi TCT phản hồi quá nhanh, dẫn đến việc chưa kịp lưu cache, đã phải handle PHKT từ TCT, tạm thời sẽ lưu cache trước
        RequestInfo requestInfo = RequestInfo.builder()
                .createdAt(Instant.now())
                .initAt(event.getInitAt())
                .appId(event.getAppId())
                .taxCode(event.getTaxCode())
                .transactionId(endAuthorizationEventStore.getMessageId())
                .messageType(MessageType.of(endAuthorizationEventStore.getMessageType()))
                .build();
        cacheService.saveRequestInfo(requestInfo);

        var future = kafkaTemplate.send(kafkaQueueIn, requestXml);

        future.addCallback(new MdcListenableFutureCallback<>(
                result -> handleSuccess(requestXml, endAuthorizationEventStore, event, topic),
                ex -> handleFailure(requestXml, endAuthorizationEventStore, event, ex)
        ));
    }

    private void handleSuccess(String requestXml,
                               EndAuthorizationEventStore endAuthorizationEventStore,
                               Event<Object> event,
                               String topic) {
        if (log.isTraceEnabled()) {
            log.trace("Sent message to TCT queue-in {} with data : {} success", kafkaQueueIn, requestXml);
        } else {
            log.info("Sent Sync Message to TCT queue-in {} success", kafkaQueueIn);
        }
        Instant now = Instant.now();
        endAuthorizationEventStore.setStatus(EndAuthorizationEventStatus.SENT.value());
        endAuthorizationEventStore.setRequestAt(now);
        repository.save(endAuthorizationEventStore);

        pushEvent(requestXml, EventType.SYNC_REQUEST_TO_TCT_HAS_BEEN_SENT, event, endAuthorizationEventStore.getRefTaxCode());
    }

    private void handleFailure(String requestXml,
                               EndAuthorizationEventStore endAuthorizationEventStore,
                               Event<Object> event,
                               Throwable ex) {
        log.info("Sent message to TCT queue-in {} fail : {}", kafkaQueueIn, ex);
        endAuthorizationEventStore.setStatus(EndAuthorizationEventStatus.SENT_FAIL.value());
        endAuthorizationEventStore.setRequestAt(Instant.now());
        repository.save(endAuthorizationEventStore);
        pushEvent(requestXml, EventType.SYNC_REQUEST_TO_TCT_FAILED, event, endAuthorizationEventStore.getRefTaxCode());
    }

    private void pushEvent(String requestXml, EventType eventType, Event<Object> event, String refTaxCode) {
        String MTDiep = getInnerTagFromSourceXml(requestXml, Constant.XML_TAG.MESSAGE_ID);
        Integer MLTDiep = Integer.valueOf(getInnerTagFromSourceXml(requestXml, Constant.XML_TAG.MESSAGE_TYPE));
        MessageType messageType = MessageType.of(MLTDiep);

        eventService.push(Event.builder()
                .appId(event.getAppId())
                .initAt(event.getInitAt())
                .createdAt(Instant.now())
                .transactionId(event.getTransactionId()) //TransactionId của thông điệp gốc cần đồng bộ với TCT
                .messageId(MTDiep)
                .messageType(messageType)
                .type(eventType)
                .taxCode(refTaxCode)
                .data(requestXml)
                .build());
    }

    private String beginTag(String xmlTag) {
        return "<" + xmlTag + ">";
    }
    private String endTag(String xmlTag) {
        return "</" + xmlTag + ">";
    }


    //1 thông điệp 202 tương ứng với 1 thông điệp 500 gửi đến TCT
    //1 thông điệp 204 tương ứng với 1 thông điệp 503 + 1 thông điệp 504 gửi đến TCT
    private List<MessageType> mappingListMessageType(MessageType sourceMessageType) {
        return switch (sourceMessageType) {

            case _202_ThongDiepThongBaoKetQuaCapMa
                    -> List.of(MessageType._500_ThongDiepChuyenHoaDonUyQuyen);

            case _204_ThongDiepThongBaoKetQuaKiemTraDuLieu
                    -> List.of(MessageType._503_ThongDiepChuyenHoaDonKhongDuDieuKienCapMa,
                               MessageType._504_ThongDiepChuyenDuLieuThongBao);

            default -> throw new IllegalArgumentException("Invalid message type");
        };
    }

    //trả về EventType để biết sẽ lấy data từ folder nào trong minio
    private EventType getEventTypeByMessageType(MessageType messageType) {
        return switch (messageType) {
            case _500_ThongDiepChuyenHoaDonUyQuyen,
                 _504_ThongDiepChuyenDuLieuThongBao
                    -> EventType.RESPONSE_TO_EINVOICE_HAS_BEEN_SENT;

            case _503_ThongDiepChuyenHoaDonKhongDuDieuKienCapMa
                    -> EventType.RECEIVED_EINVOICE_REQUEST;

            default -> throw new IllegalArgumentException("Invalid message type");
        };
    }

    public static String getOuterXmlUsingSubString(String tag, String xml) {
        String openTag = "<" + tag;
        String closeTag = "</" + tag + ">";
        int openTagIndex = xml.indexOf(openTag);
        int closeTagIndex = xml.lastIndexOf(closeTag);
        if (openTagIndex == -1 || closeTagIndex == -1) return null;
        return xml.substring(openTagIndex, closeTagIndex + closeTag.length());
    }
}
