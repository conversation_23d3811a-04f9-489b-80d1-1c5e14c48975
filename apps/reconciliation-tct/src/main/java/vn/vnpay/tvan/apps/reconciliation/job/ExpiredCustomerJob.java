package vn.vnpay.tvan.apps.reconciliation.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.vnpay.tvan.apps.reconciliation.service.ExpiredCustomerService;

import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRACE_ID;
import static vn.vnpay.tvan.libs.common.constant.Constant.KEY.TRANSACTION_ID;

@Component
@EnableScheduling
@ConditionalOnProperty(value = "scheduler.job.enable.send-mail-expired-customer", havingValue = "true")
@Slf4j
public class ExpiredCustomerJob {
    private final ExpiredCustomerService expiredCustomerService;

    public ExpiredCustomerJob(ExpiredCustomerService expiredCustomerService) {
        this.expiredCustomerService = expiredCustomerService;
    }

    @Scheduled(cron = "${scheduler.job.cron.send-mail-expired-customer}")
    public void sendReportEmail() {
        long startTime = System.currentTimeMillis();
        MDC.put(TRACE_ID, String.valueOf(startTime));

        expiredCustomerService.sendMailExpiredCustomer();

        log.info("Send mail time = {}ms", System.currentTimeMillis() - startTime);
        MDC.clear();
    }
}
